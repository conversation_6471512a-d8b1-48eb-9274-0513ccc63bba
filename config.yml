version: 5.5.5

# 888b     d888 888888b.                 888                                               
# 8888b   d8888 888  "88b                888                                              
# 88888b.d88888 888  .88P                888                                               
# 888Y88888P888 8888888K.   .d88b.   .d88888 888  888  888  8888b.  888d888 .d8888b        
# 888 Y888P 888 888  "Y88b d8P  Y8b d88" 888 888  888  888     "88b 888P"   88K        
# 888  Y8P  888 888    888 88888888 888  888 888  888  888 .d888888 888     "Y8888b.      
# 888   "   888 888   d88P Y8b.     Y88b 888 Y88b 888 d88P 888  888 888          X88      
# 888       888 8888888P"   "Y8888   "Y88888  "Y8888888P"  "Y888888 888      88888P' 
#                                                 ______  ______
#                                                / ____/ / ____/
#                 .--.--..-----..----.          /___ \  /___ \
#                 |  |  ||  -__||   _| __      ____/ / ____/ /
#                  \___/ |_____||__|  |__|    /_____(_)_____/

# Thank you for choosing Marcely's Bedwars!
# Having trouble getting it set up? Looking for a tutorial?
# Check out the official Wiki: https://wiki.mbedwars.com/
############################################################################




# 插件发送给玩家的消息将从该文件中获取（位于 'languages' 文件夹中）
# 如果同时启用了 'language-per-player'，那么这种语言也会自动成为尚未创建语言的玩家的默认语言
language-file: chinese simplified.yml

# 启用此功能将导致插件加载 'folder' 中的每个消息文件
# 然后插件将自动以玩家的语言发送消息
# RAM 使用量会增加，因为每种语言的每条消息都必须保存在内存中
# 一些消息，如记分板中的消息，可能仍然使用与 'language-file' 相同的语言
language-per-user: false

# "language-per-user" 的补充：玩家需要 mbedwars.langperuser 权限才能使其工作
# 使用此功能时，"language-per-user" 也必须设置为 true
language-per-user-requires-permission: false

# 如果启用此配置，则只有拥有 'mbedwars.beta' 权限的人才能加入
beta: false

# 如果启用此配置，则世界时间将固定为（白天）
# 此效果仅对游戏玩家和观察者可见
# 注意：时间可以为每个竞技场单独设置。这将定义新竞技场的默认行为。
always-day: true

# 如果启用此配置，则世界不会再为游戏玩家和观察者下雨
# 注意：天气可以为每个竞技场单独设置。这将定义新竞技场的默认行为。
no-rain: true

# 启用此配置会导致插件在玩家进入竞技场之前保存玩家的物品栏
# 一旦他们离开竞技场，他们的物品栏将被保存的物品栏替换
# 如果禁用，则他们的物品栏不会改变，导致他们保留从起床战争中获得的物品
# 因此不建议禁用它，除非您决定例如使用 "inventory-clear" 或不希望插件替换来自您大厅的物品
inventory-backup: true

# 启用/禁用是否在玩家离开竞技场后清除玩家的物品栏
# 当启用 "inventory-backup" 时，此配置无效
inventory-clear: true

# 如果启用此配置，人们无法将游戏模式更改为创造模式或启用飞行
anticheat-enabled: false

# 将此设置为 false 会导致在最终死亡时不显示 "玩家 x 离开了竞技场" 消息（因为被摧毁而被迫离开）
leavemessage-at-end: true

# 如果玩家使用这些命令之一，他们将离开回合
hubcommands:
- /hub
- /spawn
- /queue bedwarshub
- /leave
- /l

# 决定您是否同意共享某些可能用于全球统计的详细信息
# 我们使用它们来识别我们应该更多关注的方面（如错误等），并可能与社区分享
# 请注意，禁用此配置不会禁用更新检查器和依赖项下载器，因为它们是插件一般功能所必需的
# 我们收集以下信息：
# 此插件引起的错误/警告，服务器版本（与 "/version" 命令相同的信息），玩家数量（总数和正在玩起床战争的数量，当前和最大值）， 
# 创建的竞技场数量，使用的存储类型（仅类型，无凭据），配置的语言，IP/MAC（用于按国家聚合客户端）， 
# 活跃插件（用于识别兼容性问题引起的错误）
metrics: true

# 团队中的单独玩家（有床）是否能够在断开连接/离开时重新加入游戏
solo-rejoin-enabled: false

# 单独玩家在床被自动摧毁和团队被淘汰之前有多少秒重新加入。
solo-rejoin-time: 30

# 如果启用此配置，玩家的名字将被着色（通过数据包工作（如果他们在其他团队中，您将看不到玩家的颜色））
player-color: true

# 如果启用此配置并且当竞技场结束时，服务器将自动重启
restart-oncearenaend: true

# 如果玩家在游戏回合中将自己传送到距离其位置超过 12 个方块的地方，他们将被踢出
# 管理员仍然被允许传送
kick-outofarena: true

# 如果玩家在大厅内传送自己，他们将被踢出
kick-outofarena-lobby: true

# 禁用此功能将删除玩家进入或离开服务器时出现的消息
# 如果为 true，此插件不会对这些消息进行任何更改
server-joinquitmessage-enabled: true

# 将此设置为 false 将（几乎）完全禁用操作栏消息的使用
actionbar-enabled: false

# 将在操作栏中显示的消息
# 为此，"actionbar-enabled" 必须设置为 true
# 占位符：{team-color}, {team-display-name}, {team-players}, {players-per-team}
actionbar-ingame-text: '{team-color}{team-display-name} &8[&7{team-players}/{players-per-team}&8]'

# 将向游戏玩家隐藏不在回合中的玩家
# 观察者也会被隐藏
# 观察者只会看到自己和回合内的玩家
# 特殊情况，如果启用了 "endlobby-tptolobby"：观察者将在回合结束后变为可见
tab-removenonplayers: true

# 将为非游戏玩家隐藏游戏玩家和观察者
# 启用此功能时，没有人会看到观察者
tab-removeplayers: true

# 将此配置设置为 false 时，竞技场内不会生成生物（动物和怪物）
# 大多数插件不应受此影响
natural-mob-spawning-enabled: false

# 定义生物生成可以接受的情况
# 完整列表：https://hub.spigotmc.org/javadocs/spigot/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html
# 请注意，并非所有生成原因都存在于旧版本中
natural-mob-spawning-reasons-whitelist:
- SLIME_SPLIT
- CUSTOM
- EXPLOSION
- COMMAND
- SHOULDER_ENTITY
- SPAWNER_EGG
- SHEARED
- DEFAULT

# 如果您使用升级，则需要此配置，因为它设置基地大小的半径
# 中心点既是床的位置也是团队生成点，在它们任一半径内的所有内容都将被视为基地的一部分
# 这是半径，不是直径！它具有圆形形状，不是立方体
upgrade-spawnsize: 20

# 如果禁用此配置，则玩家的饥饿等级不会改变（仅在游戏中）
hunger: false

# 增加此值将增加玩家从跌落中受到的伤害量
fall-damage-multiplier: 1.0

# 禁用此功能会导致玩家无法与特定材料交互
# 这些包括：工作台、铁砧、投掷器、发射器、熔炉、信标、漏斗、附魔台、
#   制箭台、烟熏炉、高炉、切石机、锻造台、制图台、酿造台、
#   告示牌和花盆
interacting: false

# 玩家获得成就时将发送的消息
# 占位符：{name}, {description}
earnachievement-message:
- '&7&m========================='
- '&6           &l成就已获得'
- '&f{name}: '
- '&d {description}'
- '&7&m========================='

# 禁用/启用大厅中的自动团队平衡
# 它试图使团队尽可能公平
# 警告：禁用此功能将使所有玩家都可以加入同一个团队（从而没有敌人）
# 只有在您有自己的实现或与您信任的人一起玩时，才建议禁用此功能
teambalance: true

# 如果启用此配置，则以 'giveitems-on-' 开头的配置将起作用
giveitems-on-enabled: true

# 在此指定回合开始时应给予哪些物品
# 为此必须启用 'giveitems-on-enabled'！
giveitems-on-roundstart:
- wooden_sword

# 在此指定回合开始时应将哪些盔甲物品设置到物品栏槽位
# 为此必须启用 'giveitems-on-enabled'！
giveitems-on-roundstart-armor:
- leather_helmet {Unbreakable:1b}
- leather_chestplate {Unbreakable:1b}
- leather_leggings {Unbreakable:1b}
- leather_boots {Unbreakable:1b}

# 在此指定重生时应给予哪些物品
# 为此必须启用 'giveitems-on-enabled'！
giveitems-on-respawn:
- wooden_sword {Unbreakable:1b}

# 在此指定重生时应将哪些盔甲物品设置到物品栏槽位
# 为此必须启用 'giveitems-on-enabled'！
giveitems-on-respawn-armor:
- leather_chestplate {Unbreakable:1b}
- leather_helmet {Unbreakable:1b}
- leather_leggings {Unbreakable:1b}
- leather_boots {Unbreakable:1b}

# 在此指定玩家重生后应获得哪些药水效果
# "giveitems-on-enabled" 对此无效
# 用法：<药水效果名称>:<持续时间（刻）（20 刻 = 1 秒）>:<等级>
giveeffects-on-respawn: []

# 如果为 false，玩家将无法移动或丢弃其物品栏中的盔甲
armor-interactable: false

# 此命令在回合开始时执行
executeon-roundstart: ''

# 配置添加到 "blocked-commands" 的命令的处理方式
# 您可以选择：
#  - DISABLED：配置无效
#  - BLACKLIST：特定命令将被禁止
#  - WHITELIST：除这些命令外，所有命令都将被阻止
#
# 管理员可以通过拥有 "mbedwars.bypassblockedcommands" 权限来绕过限制
blocked-commands-mode: BLACKLIST

# 这些命令将被阻止/允许玩家和观察者使用
# 使用 "blocked-commands-mode" 配置来配置确切的行为
# 语法：/<命令>
# 示例：/tpa
#  -> 这将阻止 "/tpa" 的所有变体，包括 "/TpA" 或 "/tpa player123"
blocked-commands:
- /tpa
- /sethome
- /lock
- /tpahere
- /tpaccept
- /enderchest

# 拥有隐身药水效果时，盔甲自然可见
# 此配置允许您在设置为 true 时隐藏玩家的盔甲
invisibility-hides-armor: true

# 如果启用此配置，隐身药水效果的粒子将被移除
# 配置 "invisibility-hides-armor" 必须启用才能使其工作
invisibility-hides-armor-remove-particles: false

# 您可以在玩家隐身时生成更多药水粒子
# 值越大，生成的粒子越多
# 配置 "invisibility-hides_armor_remove-particles" 必须启用，"invisibility-hides-armor" 必须禁用才能使其工作
invisibility-hides-armor-extra-particles-count: 4

# 团队箱子可以被团队的所有玩家打开，从竞技场内配置类型的任何箱子
# 有些人可能更喜欢使用普通箱子作为团队箱子
teamchest-enabled: true

# 指定应将哪种箱子变体用作团队箱子
# 您可以在 ENDER_CHEST 和 CHEST 之间选择
teamchest-block: chest

# 如果为 true，玩家将无法在竞技场外从商店购买物品
# 这对于允许玩家在大厅中编辑他们的快速购买可能很有用
block-purchases-outside-running-arenas: false

# 默认情况下，如果物品不适合玩家的物品栏，它将掉落在他们旁边的地面上
# 这将阻止玩家购买不适合其物品栏的物品
block-purchases-when-inventory-full: false

# [开发者] 定义 MBedwars 是否应该尊重其他插件的事件取消
# 受此配置影响的事件有：BlockPlace、BlockBreak、EntityDamage、EntityDamageByEntity
# 如果设置为 true，则如果其中一个事件被另一个插件取消，插件将不会执行任何操作
# 建议将此保持为 false，否则可能导致意外行为
# 此配置的一个目的是修复与反作弊的潜在冲突
respect-event-cancellations: false


# ========== 大厅 ==========

# 使用强制开始时应设置的倒计时时间
forcestart-time: 20

# false：强制开始大厅物品和命令 "/bw forcestart" 需要满足最少玩家数量
# true：强制开始大厅物品和命令 "/bw forcestart" 可以使用，即使他们是竞技场中唯一的人
forcestart-ignoreminplayers: false

# 当大厅中的倒计时开始时使用此计算。
# 将此值更改为低值是危险的。
# 占位符：{teams}, {teamplayers}
lobby-countdownstart-calculation: '90'

# 玩家在被送回大厅之前必须等待多少秒
# 点击离开物品。他们可以在此时间内再次点击来取消离开。
# 设置为 0 使其立即生效
lobby-leaveitem-time: 3

# 当达到竞技场中玩家的百分比时自动缩短倒计时
# 当竞技场中有足够的玩家时，此功能有助于减少等待时间
# 将其设置为 101 以禁用此功能
auto-shorten-countdown-percent: 70

# 与 "auto-shorten-countdown-percent" 相关
# 这是当达到玩家百分比时倒计时将缩短到的时间（以秒为单位）
auto-shorten-countdown-time: 15

# 如果为 true，当玩家加入或离开竞技场大厅时将播放粒子动画
lobby-join-leave-particles: false

# 如果启用此配置，经验条将被动画化
# 动画包括经验条上剩余时间的缓慢减少
# 以及显示开始前剩余秒数的等级
lobby-exbar-animation: true

# 如果启用此配置，配置 'lobby-printmapinfo-lines' 的行将在第 10 秒在大厅中打印
lobby-printmapinfo-enabled: true

# 游戏开始前将打印的行
# 占位符：{arena}, {madeby}, {players}, {maxplayers}, {teams}, {teamsize}
# 您可以使用 ^# 来创建空间
lobby-printmapinfo-lines:
- '&a▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'
- '&f               &f&l起床战争'
- '&f'
- '&f    &e&l保护你的床并破坏敌人的床.'
- '&f     &e&l通过收集资源升级你自己和你的团队'
- '&f   &e&l资源点产生的铁、金、绿宝石和钻石还有红石'
- '&f             &e&l获得强大的升级.'
- '&f'
- '&a▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬'

# 此消息将与 printmapinfo 同时显示
# 占位符：{arena}, {players}, {maxplayers}, {author}
lobby-countdowntitle-arena: ''

# 这些标题将在给定的秒数内显示，直到游戏结束
lobby-countdowntitles:
  '1': '&c1'
  '2': '&c2'
  '3': '&c3'
  '4': '&e4'
  '5': '&e5'
  '9': ' '
  '10': '&a10'

# 如果启用此配置并且玩家正在更改，那么他将穿着染色物品 'lobby-team-onchange-wearcloth-material'
lobby-team-onchange-wearcloth-enabled: false

# 玩家将穿着的盔甲
# 为此必须启用 'lobby-team-onchange-wearcloth-enabled'
lobby-team-onchange-wearcloth-materials: []

# 是否应在结束大厅中显示统计信息
lobby-endstats-enabled: false

# 是否应在结束大厅中显示玩家被踢出前的时间
endlobby-show-kick-time: false

# 玩家应在结束大厅状态中保持多长时间，直到他们被踢出
# 更改此配置的值是危险的，可能导致意外和不需要的问题
endlobby-countdown-time: 15

# 玩家是否能够在结束大厅中飞行
endlobby-flying: true

# 将此设置为 true 会导致所有剩余玩家和观察者在结束大厅阶段被传送回大厅
# 通过这种方式，他们的物品栏被清理，观察者再次变为可见等。
# 如果设置为 false，则玩家和观察者停留在他们已经在的地方，不会被移回
# 当使用 lobbybreak 功能时，将此设置为 false 可能很有用
endlobby-tptolobby: false

# 竞技场开始后是否应摧毁大厅
# 注意：如果您的大厅在竞技场外，此配置将无效
lobbybreak-enabled: true

# 以大厅点为中心，方块将被摧毁的半径
# 为此必须启用 'lobbybreak-enabled'
lobbybreak-radius: 30


# ========== 竞技场投票 ==========

# 设置应投票的竞技场的最大数量
arenavoting-maxarenas: 3


# ========== 自动竞技场克隆 ==========

# 此功能*自动*创建竞技场的副本，从而始终有可供加入的竞技场。
# 克隆的竞技场经过比赛后，它将被删除并生成一个新的。稍后禁用此配置是安全的，垃圾世界将自动清理。
#
# 它适用于 REGION 和 WORLD 竞技场：
# - 如果您的竞技场是 WORLD，则在竞技场存在时会临时创建一个新世界。这发生在每个克隆的竞技场中，意味着您可能会遇到世界数量和 RAM 使用量的大幅增加
# - 如果您的竞技场是 REGION，则所有克隆的比赛区域都收集在一个世界中。每个都有偏移，意味着它们不会相互碰撞
#
# 关于竞技场大厅的一件事：建议它们在比赛区域内，否则它们不会为克隆的竞技场移动。
# 如果大厅在比赛区域外，加入同类竞技场的玩家可能会看到彼此。
# 在这种情况下，启用 "tab-removenonplayers" *可能*有意义，因为来自其他竞技场的玩家不会被看到。
auto-cloning-enabled: false

# 定义应从自动克隆中排除的竞技场名称
auto-cloning-excluded-arenas:
- second-excluded-arena
- first-excluded-arena

# 应为每个竞技场提供等待玩家的克隆竞技场数量（处于 LOBBY 状态的竞技场）
auto-cloning-lobby-arenas-count: 1

# 与配置 "auto-cloning_lobby-arenas-count" 相关
# 将此设置为 true 会导致一旦处于 LOBBY 状态的竞技场满员就提供新的克隆竞技场
auto-cloning-lobby-arenas-exclude-full: false

# 应为竞技场类型提供哪些模式（每队玩家数）
# 基本上，您可以为克隆的竞技场自动分配每队的某些玩家
# 如果没有为竞技场指定模式，该竞技场将只克隆到相同的玩家数量
# 只有拥有与 "auto-cloning-lobby-arenas-count" 配置中指定的相同数量或更少的模式才有意义
#
# 左侧：竞技场选择器（参见 https://s.marcely.de/mbww15）。定义模式适用的竞技场
# 右侧：应为给定竞技场提供的模式
auto-clone-mode-types:
  '[teams=4]':
  - '3'
  - '4'
  '[teams=8]':
  - '1'
  - '2'

# 同时可能存在的竞技场克隆数量
# 使用 -1 表示没有任何限制
auto-cloning-max-per-arena: -1

# 新创建的世界应具有的前缀
# 不建议在启用自动克隆后更改此设置。或者更好的是，保持此配置不变
# 非常重要的是，您要理解这些世界会被自动删除，如果不小心操作，您可能会丢失某些东西
auto-cloning-world-prefix: MBedwars-autoclone-


# ========== 床 ==========

# 如果启用此配置，玩家可以摧毁他们团队的床
ownbed-destroyable: false

# 设置在回合中应被摧毁的方块
bed-block: minecraft:red_bed

# 启用时，插件将自动将床染成相应团队的颜色
bed-block-dye: true

# 玩家摧毁床时将掉落的物品生成器
bed-drops-type: gold

# 玩家摧毁床后将掉落的材料数量（它将采用配置 'bed-drops-type' 的生成器材料）
bed-drops-amount: 0

# 是否可以用 TNT 或其他类型的爆炸（如火球、TNT 羊等）破坏床
bed-destroyableby-tnt: false

# 启用此配置将使玩家无法用手破坏床
# 然后他们必须使用 TNT 或引起爆炸的东西（如火球）来破坏它
# 确保 "bed-destroyableby-tnt" 设置为 true，否则此配置不会有任何效果
bed-onlydestroyablewith-tnt: false

# 启用此配置将在每个活着的床上方生成一个全息图，显示配置 'bed-hologram-message-alive' 的消息
bed-hologram-enabled: true

# 当床没有被破坏时，作为全息图显示在床上方的消息。为此必须启用 'bed-hologram-enabled'！
# 占位符：{teamcolor}, {team}, {heart}
bed-hologram-message-alive: '&c保护您的床!'

# 禁用此功能将导致玩家无法与您在设置期间放置的床方块交互
# 这将导致 '您只能在夜间睡觉' 消息不出现
bed-interactable: false


# ========== 生成器 ==========

# 如果启用此配置，铁和金在生成时会产生粒子
itemspawner-effect: true

# 如果启用此配置，铁和金在生成时会发出声音
itemspawner-sound: false

# 如果启用此配置，所有物品生成器将自动在您放置它们的方块中居中。
# 如果您想要能够设置确切位置，请将此设置为 false。
itemspawner-auto-center: true

# 物品生成器全息图的高度
spawnerhologram-height: 2.0

# 物品生成器全息图的动画速度
spawnerhologram-speed: 5

# 启用此功能将导致位于玩家基地内的生成器的全息图被隐藏
# 使用配置 "upgrade-spawnsize" 来指定团队生成的半径
spawnerhologram-hide-playerbase: false

# "智能物品共享" 是一个使掉落的物品在玩家之间平等共享的系统
# 原版 Minecraft 系统通过偏爱一个玩家而其他人什么都得不到来工作
# 这只适用于物品生成器掉落的物品
smart-item-sharing-enabled: false


# ========== 统计 ==========

# 当他们在竞技场内输入 /stats 时显示他们的统计信息
allowcommand-stats: true

# "allowcommand-stats" 的补充：即使他们不在竞技场中也允许他们这样做
allowcommand-stats-global: false

# 将用于计算排名顺序的统计信息
ranking-sortation: bedwars:wins

# 玩家排名的计算频率（以分钟为单位）
# 将此设置为 -1 也将完全禁用自动计算。您必须使用 /bw tools recalcstats 来更新它们
recalculate-ranking-auto-period: 60

# 应如何显示游戏时间
# 占位符：{days}, {hours}, {minutes}, {seconds}。将从下面的配置中获取它们
display-counting-time: '{days} {hours} {minutes} {seconds}'

# 应为 "display-counting-time" 中的占位符插入什么
display-counting-time-entry-days: '{value}天'
display-counting-time-entry-hours: '{value}时'
display-counting-time-entry-minutes: '{value}分'
display-counting-time-entry-seconds: '{value}秒'

# 如果为 true，我们只会在 "display-counting-time-entry-*" 占位符不等于 0 时插入它们
display-counting-time-entry-only-nonnull: true

# 用于启用防止玩家通过滥用系统重复获得统计信息的系统
# 这包括与小号进行 1v1 并让他立即离开（从而您的主号获得胜利计数）
stats-antiabuse-enabled: true

# 请参阅 "stats-antiabuse-enabled" 以启用此功能并了解它的内容
# 特别是以下情况，如果过去的 x 轮在 y 分钟内获胜，则统计信息不会被计算
stats-antiabuse-count-round-min-duration: 5.0
stats-antiabuse-count-wins-count: 2


# ========== 方块破坏/放置 ==========

# 奇怪/不寻常的方块掉落应该发生什么
# （如掉落线的蜘蛛网或掉落黑曜石的末影箱）
# 可能的选项：
#  - KEEP = 不改变掉落物
#  - REPLACE_WITH_BLOCK_MATERIAL = 用破坏的方块替换掉落物
#  - REMOVE = 完全移除掉落物，使它们什么都不掉落
unusual-blockdrops-handling: REPLACE_WITH_BLOCK_MATERIAL

# 将此设置为 false 会取消方块传播，这包括例如：
# - 火焰传播/火焰破坏方块。它不会阻止火焰首先存在
# - 蘑菇传播
block-spreading-enabled: false

# 将此设置为 false 会取消方块形成，这包括例如：
# - 由于暴雪而形成的雪（即使启用了 no-rain，这也可能发生）
# - 在像针叶林或苔原这样的雪地生物群系中形成的冰
# - 由于与水接触而形成的黑曜石/圆石
# - 由于混凝土粉末和水的混合而形成的混凝土
block-forming-enabled: false

# 如果启用此配置，树叶不会在竞技场内腐烂
prevent-leaves-decay: true

# 如果禁用此配置，以 'notbuildableradius-' 开头的配置将不起作用
notbuildableradius-enabled: true

# 玩家不能在商店和升级商店周围放置/破坏方块的半径（0 = 禁用）
notbuildableradius-dealers: 3

# 玩家不能在团队生成点放置/破坏方块的半径（0 = 禁用）
notbuildableradius-teamspawn: 3

# 玩家不能在物品生成器处放置/破坏方块的半径（0 = 禁用）
notbuildableradius-itemspawner: 3

# 防止水流入不可建造半径
notbuildableradius-permit-block-liquidflow: true

# 如果启用此配置，并且玩家购买例如羊毛，羊毛将被染成他的团队颜色
dye-block: true

# 如果启用，方块在被拾取时将重新染成团队颜色。
# 注意：如果禁用 "dye-block"，这没有效果。
redye-blocks-onpickup: true

# 默认情况下（此配置设置为 true），只有特定的方块材料是可放置的。这包括商店中可用的材料。
# 您可以使用 "placeableblock-whitelist" 配置将您自己的材料添加到白名单中。
# 但是，您可以通过将此配置设置为 false 来完全禁用白名单。通过这种方式，所有材料都变得可破坏和可放置。
# 请记住，与材料无关的配置，如 "destroyblock-builtbyplayers"，仍然可以对限制可破坏的方块产生影响。
placeableblock-whitelist-enabled: true

# 在此配置中，您可以添加玩家可以放置的方块
# 可购买的方块自动在此列表中
# 示例：placeableblock-whitelist: wood, dark_oak_door, cake
placeableblock-whitelist: []

# 如果禁用此配置，玩家不能再践踏小麦
destroyable-farmland: true

# 玩家将能够通过左键单击来扑灭火焰
destroyable-fire: true

# 此配置将导致玩家只能摧毁竞技场中其他玩家放置的方块。
# 通过这种方式，不可能破坏竞技场。
destroyblock-builtbyplayers: true

# 悬挂实体（如画、物品展示框、拴绳等）是否可交互和可破坏
# 请参阅 https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/Hanging.html "所有已知子接口" 了解什么算作悬挂
hanging-interactable: false

# 如果启用此配置，地面植物（花、草、树苗等）不会妨碍玩家（在 PvP 期间）
# 这意味着对植物破坏的限制将被解除，它们不会掉落任何东西
floor-foliage-simplified-destruction: true


# ========== 爆炸物 ==========

# TNT 在放置时将自动点燃
tnt-autoignite: true

# 时间（以秒为单位） until the auto-ignited TNT explodes
# 不建议过多修改此值，因为它可能会破坏动画
# 需要启用 tnt-autoignite
tnt-fuse-time: 3.0

# 自动点燃的 TNT 将造成的爆炸产量/大小
# 需要启用 tnt-autoignite
tnt-yield: 4.0

# 如果禁用此配置，爆炸不会破坏任何方块
explosion-canbreakblocks: true

# 如果启用此配置 那么爆炸只会破坏玩家放置的方块
# 确保同时将 "explosion-canbreakblocks" 设置为 true，否则此配置不会有任何效果
explosion-canbreakblocks-breakby-player: true

# 除其他外，这些方块不会被 TNT 或爬行者破坏
explosion-blocks-blacklist:
- white_stained_glass

# 不会受到爆炸伤害的实体
explosion-entities-blacklist:
- ITEM_FRAME

# 增加此值将增加玩家被 TNT 等爆炸物推开的距离，但不包括火球
# 它通过将给定轴上的速度乘以下面配置的值来工作
# 您可以 配置击退 对于 xz 轴和 y 轴
# 0 = 无击退 | 1 = Default | 2 = 击退 2x 更大 | 3.5 = 击退 3.5x 更大 | ...
explosive-multiplier-knockback-y: 1.5
explosive-multiplier-knockback-xz: 2.0

# 此数量在与上述值相乘之前被添加到 y 轴的速度中
# 0 是默认的原版值
explosive-add-knockback-y: 0.5

# 给定轴上的速度被限制为下面配置的值
explosive-max-knockback-y: 3.0

# 增加此值将增加玩家从 TNT 等爆炸物中受到的伤害量
explosive-multiplier-damage: 0.1

# 增加此值将增加玩家被火球推开的距离
# 它通过将给定轴上的速度乘以下面配置的值来工作
# 您可以 配置击退 对于 xz 轴和 y 轴
# 0 = 无击退 | 1 = 原版 | 2 = 击退 2x 更大 | 3.5 = 击退 3.5x 更大 | ...
fireball-multiplier-knockback-y: 1.5
fireball-multiplier-knockback-xz: 2.0

# 此数量在与上述值相乘之前被添加到 y 轴的速度中
# 0 是默认的原版值
fireball-add-knockback-y: 0.5

# 给定轴上的速度被限制为下面配置的值
fireball-max-knockback-y: 3.0

# 指定对敌人在所有 xyz 轴上造成的自定义击退倍数
# 上面的 fireball_*_knockback_* 配置仅在射手击中自己时应用（火球跳跃）
# 如果您希望敌人和射手具有相同的击退，请将此设置为 0.0
fireball-multiplier-knockback-enemy: 0.0

# 射手用自己射出的火球击中自己时将承受的倍增伤害量
# 1 = 原版，0 = 无，2 = 2 倍伤害
fireball-multiplier-damage: 0.0

# 敌人被火球击中时将受到的倍增伤害量
# 1 = 原版，0 = 无，2 = 2 倍伤害
fireball-multiplier-damage-enemies: 1.0

# 配置火球是否应直线飞行
fireball-fly-straight: true

# 此配置 defines the fly speed of a fireball
# 将此配置设置得太低可能会导致错误。默认值为 1.0
fireball-fly-speed: 2.0

# 如果启用此配置, 火球将以恒定速度飞行
# 禁用此功能后, 火球将随时间加速，就像原版中一样
fireball-fly-speed-constant: true

# 火球将造成的爆炸产量/大小
fireball-yield: 1.5

# 如果设置为 true，玩家在空中使用火球并向下看时将导致火球立即点燃
# 通过这种方式，他们基本上可以执行二段跳
fireball-air-jump: true

# 数量 ticks after usage until the fireball ignites
# 此配置 is only relevant when "fireball-air-jump" is enabled
fireball-air-jump-ticks: 3

# 影响配置：explosion-blocks-blacklist，tnt-canbreakblocks-breakby-player
# 将此设置为 true will allow only affected blocks to be destroyed by explosions (fireball, tnt), and not blocks covered by blast-proof blocks
# 说明：[D][O]  X
# X 是爆炸，[D] 是普通方块，[O] 是防爆的。当此配置设置为 false 时，[D] 有可能被摧毁
# 此配置 requires additional complex math and may cause lag
explosion-raycheck: true

# 如果启用此配置, 爆炸物（TNT，火球等）将摧毁附近的物品
explosion-destroys-items: false


# ========== 死亡 ==========

# 如果启用此配置, 将在玩家死亡的位置创建粒子
particles-ondeath: true

# 启用时尝试不显示 "您死了！" 屏幕
# 对 1.15+ 的增强支持：屏幕将完全跳过
# 由于技术限制，1.14 及更早版本会有超短暂的闪烁
death-skipscreen: true

# 玩家将 enter the spectator mode for a short time when they die
death-spectate-enabled: true

# 玩家死亡时是否应留在原地观察，还是前往观察者生成点
death-spectate-at-death-location: true

# "death-spectate-enabled" 配置的补充：
# 配置玩家应作为观察者停留多长时间
death-spectate-time: 3

# 将 "重生倒计时" 消息显示为标题，而不是在快捷栏上方
death-spectate-respawnmsg-astitle: true

# 定义重生玩家在重生后不会受到敌人伤害的秒数
# 如果重生的玩家尝试攻击，重生保护将立即被移除
# 0 或更少将禁用重生保护
respawn-protection: 1

# 启用时: Players will instantly die when they touch water
diein-water: false

# 定义当玩家从竞技场掉落时应使用什么方法来确定何时自动杀死玩家
# 可能的选项有：
# - DISABLED：他将像原版中已知的那样慢慢受到伤害
# - BOTTOM_ARENA_BORDER: The player will die when he reaches the bottom of the arena
# - VOID: The player will die when he obtains void damage (-70 blocks below world end)
# - Y_LEVEL: The player will die when he reaches a certain y level (see config "diein-boundary-y-level")
diein-boundary-method: BOTTOM_ARENA_BORDER

# 玩家将死亡的 y 级别
# 此配置 is only relevant when "diein-boundary-method" is set to Y_LEVEL
diein-boundary-y-level: 0

# 如果玩家在比赛期间死亡，不会掉落物品和经验
# 注意：如果 "drop-only-itemspawner" 处于活动状态，此配置无效
no-drops: true

# 玩家将 only drop the spawner items that they were previously carrying
# 如果有经验，经验也会掉落
# 此配置 has a 更大 priority than the "no-drops" config
drop-only-itemspawner: true

# 启用时，杀手将自动拾取受害者的掉落物
# 如果没有杀手，根本不会掉落物品和经验
drops-killer-auto-pickup: true

# 如果玩家在床被摧毁后不久在虚空中死亡，他们的死亡将作为击杀奖励给床的摧毁者
# 时间由配置 "rage-quit-auto-detect" 定义
reward-recent-deaths-to-bed-destroyer: true

# 如果玩家离开以试图阻止伤害者获得击杀，伤害者仍然会获得击杀
# 此配置 will also detect if a player leaves after a bed is destroyed, and rewards the kill to the bed destroyer
rage-quit-auto-detect: true

# 时间（以秒为单位） after getting damaged / his bed destroyed in which a player is considered to have rage quit, in case he leaves
# 此配置 is only relevant when "rage-quit-auto-detect" is enabled
rage-quit-auto-detect-max-time: 15


# ========== 死亡消息 ==========

# 它将完全移除死亡消息。如果启用，任何其他 'deathmessage-*' 都不会有任何影响！
deathmessage-remove: false

# 如果启用此配置, 游戏玩家的死亡消息对非游戏玩家不可见
deathmessage-private: true

# 如果启用此配置, 死亡消息将被更改
deathmessage-custom-enabled: true

# 自定义死亡消息应该是什么样子
# 占位符：{player}, {killer}, {team}, {teamcolor}, {killerteam}, {killerteamcolor}, {heartpercent} (0-100), {heartvisibleamount} (0-10), {heartamount} (0-20)
deathmessage-custom-killed:
- '%Deathmessage_Killed%'

# 这些的占位符：{player}, {team}, {teamcolor}
deathmessage-custom-void:
- '%Deathmessage_Void%'
deathmessage-custom-explosion:
- '%Deathmessage_Explosion%'
deathmessage-custom-fall:
- '%Deathmessage_Fall%'
deathmessage-custom-fire:
- '%Deathmessage_Fire%'
deathmessage-custom-default:
- '%Deathmessage_Default%'


# ========== 奖励 ==========

# 如果启用此配置 将给获胜者和失败者奖励
prize-enabled: true

# 命令 that'll be executed for every player who wins a round
# 还使用 vault 来识别他是否赚了钱，并向他发送带有金额的消息
# 参数：{name}, {uuid}
prize-commands:
- eco give {name} 50
- alonsolevel addexp {name} 400

# 命令 that'll be executed for every player who loses a round
# 参数：{name}, {uuid}
prize-loser-commands:
- eco give {name} 10
- alonsolevel addexp {name} 200


# ========== 性能 ==========

# 此配置 essentially allows you to regulate how often you want stuff to get updated
# 它对游戏玩法有轻微影响，因此建议至少使用 "Normal" 以获得最佳体验
#
# 选项：
#  - Very_Low：动画看起来会更差，但性能会更好
#  - Low
#  - Normal: Looking good and not having to sacrifice too much performance
#  - High
#  - Ultra: Animations will look great but might decrease performance
#
# 它影响的内容：
#  - 物品生成器全息图的动画流畅度
#  - Animation fluency of expbar in lobby
#  - Frequency of border getting updated/displayed
#  - Frequency of upgrade's base effects getting updated
#  - Frequency of enemies getting checked for entering traps
performance: Ultra

# 更改竞技场的再生速度
# 仅适用于以区域作为再生类型的竞技场
# 该值基本上定义了用于处理它的刻的百分比
# 100% 将消耗整个刻，从而导致延迟。1% 不会导致任何延迟，但完成时间会更长
regeneration-speed-percentage: 15.0

# 同时可以再生多少个竞技场
# 竞技场 is getting added to a queue when the configured amount gets reached
# 世界竞技场总是一次性再生以避免服务器冻结 -> 此配置仅对区域竞技场有效
regeneration-amount-at-the-same-time: 2

# 为区域竞技场使用更优化的再生器
# 通过这个，我们注意到大型竞技场的再生速度提高了约 40 倍
# 请注意，对于较旧的存储区域文件，它仍将使用传统方式。在这种情况下，您将在控制台中收到警告
regeneration-region-efficient: true

# 还为区域竞技场存储和再生生物群系（仅限 1.15+）
# !! 启用此功能后，再生时间增加 30 倍！
# 请注意，启用此功能后必须再次保存方块（/mbedwars arena saveblocks <arena>）
# 您可以 use "/mbedwars arena saveblocks *" (keep the star) to execute this automatically for all of your arenas
# 还要注意，禁用此功能后必须再次保存方块，否则您不会注意到包含生物群系的竞技场有任何差异
regeneration-region-store-biomes: false

# 启用时将使用 SlimeWorldManager 存储和再生世界。
# SlimeWorldManager 不一定比我们的格式更快。您可能想要在区域、世界和 swm 之间进行测试，看看哪个最适合您。
# 仅适用于类型为 "world" 的竞技场。您可以使用 "/bw arena info <arena name>" 检查竞技场是否使用该格式。
# 确保世界已被 SWM 加载，否则插件将保持我们的格式。
# 此外，禁用 SlimeWorldManager 会导致当前使用该格式的竞技场损坏。
# 要迁移到或从格式迁移，请使用 "/bw arena saveblocks <arena name>"。确保您的竞技场是干净的，您可能想要事先运行 "/bw arena regenerate <arena name>"。
slimeworldmanager-enabled: true

# 如果启用此配置，将显示区域竞技场角落的边界
border: true

# 定义是否要使用使用更少流量的替代边界渲染器
# 缺点是它看起来不那么漂亮
# "border" 必须首先设置为 true 才能使此功能正常工作
border-efficent-alternative: false


# ========== 游戏内/回合计时器 ==========

# 如果启用此配置 那么竞技场将在倒计时停止后自动停止
timer-enabled: true

# 数量 time until an arena will end by itself (in seconds)
timer: 3600


# ========== 观察 ==========

# 如果启用此配置, 不在游戏中的人可以观看正在进行的游戏
spectating: true

# 如果启用此配置，死亡的玩家将自动进入观察者模式
spectator-autojoin: true

# 玩家可以使用 ChangeSpeed 物品选择的速度类型
# Each type should be build like this: <name>:<speed>
# 默认速度为 1，最大值为 10
spectator-changespeed-types:
- '&a无:1.0'
- '&aI:1.5'
- '&aII:2.0'
- '&aIII:2.5'
- '&aIV:3.0'

# 如果禁用此配置, players will not see the teleporter GUI when they are in death spectate
spectator-tp-gui-visible-on-death-spectate: true

# 观察者是否应该有夜视效果
spectator-nightvision: true

# 启用此配置后，观察者可以右键单击游戏中的玩家以在其视角内观察
spectator-permit-other-player-view: true

# 如果启用此配置, 观察者不得超越竞技场的边界
# 这仅对区域竞技场有效。即使禁用此功能，它们也会受到世界最小和最大高度的限制以避免复杂情况
# 通常，您不想禁用此功能，除非您确定世界中只有一个竞技场，否则都是虚空
spectator-restricted-by-border: true


# ========== 特殊物品 ==========

# 如果启用此配置, 玩家将无法购买特殊物品，除非他们被允许购买。
# 权限 每个特殊物品的权限是：mbedwars.specialitem.<special item id>
# ID 列表：https://s.marcely.de/mbww3
specialitem-requiredpermission: false

# 材料 of the item for each special item
teleporter-item: gunpowder
minishop-item: armor_stand
rescueplatform-item: blaze_rod
tntsheep-item: pig_spawn_egg
magneticshoes-item: golden_boots
trap-item: string
bridge-item: blaze_rod
guarddog-item: sheep_spawn_egg
fireball-item: fire_charge
magic-milk-item: milk_bucket

# 如果启用此配置, 玩家必须等待几秒钟才能被传送
teleporter-countdown-enabled: true

# 玩家必须等待直到被传送的时间（以秒为单位）
teleporter-countdown: 5

# 速度 of the MiniShop
minishop-speed: 0.2

# 时间（以秒为单位） until the MiniShop will disappear
minishop-existence-time: 20

# 救援平台的宽度
rescueplatform-width: 2

# 材料 that will be placed when using the rescue platform
rescueplatform-material: minecraft:slime_block

# 玩家降落在救援平台上时是否应该受到伤害
rescueplatform-nodamage: false

# 救援平台是否应该在指定时间后自动被摧毁
rescueplatform-autobreak-enabled: true

# 时间（以秒为单位） when the rescue platform should get destroyed
rescueplatform-autobreak-time: 10

# 玩家和生成的救援平台之间 y 坐标的差异
# 建议至少设置为 -2，否则玩家可能无法到达
rescueplatform-y-difference: -2

# 速度 of the TNT Sheep
tntsheep-speed: 0.33

# 调整 TNT 羊造成的伤害
tntsheep-damage-multiplier: 1.0

# 时间（以秒为单位） until the TNT on the TNT sheep explodes
# 不建议过多修改此值，因为它可能会破坏动画
tntsheep-fuse-time: 4.0

# 最大值 length of the bridge
bridge-maxlength: 32

# 玩家是否真的需要在库存中有 "bridge-material" 来建造桥梁
# 将此设置为 false will also cause them to not be taken from the inventory
bridge-needsmaterials: true

# 材料 that will be placed
# 当启用 "bridge-needsmaterials" 时，它还会从玩家的库存中取出这个方块
bridge-material: minecraft:white_terracotta

# 玩家必须等待直到可以再次使用追踪器的时间（0 = 立即）
tracker-delay: 10.0

# 在此指定玩家踩到敌人陷阱后应获得哪些药水效果
# 用法：<药水效果名称>:<持续时间（刻）（20 刻 = 1 秒）>:<等级>
effects-given-on-trap-trigger:
- SLOW:160:3
- BLINDNESS:80:2

# 陷阱是否抗爆炸
traps-resistant-to-explosions: false

# 守卫犬的实体类型
# 类型完整列表：https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/EntityType.html
guarddog-type: IRON_GOLEM

# 数量 health with which the guarddog will spawn
guarddog-health: 100

# 如果为 true，守卫犬将留在其团队的床周围击退敌人
guarddog-stay-at-base: true

# 如果为 true，守卫犬不会受到队友的伤害
guarddog-prevent-team-damage: true

# 可用于增加或减少守卫犬造成的伤害
guarddog-damage-multiplier: 1.0

# 守卫犬将存活多少秒。设置为 -1 使它们永远存活（直到被玩家杀死）。
guarddog-alive-duration: -1

# 当玩家看着守卫犬时在其头上显示的文本
# 占位符：{team-color} {team} {seconds-lived} {seconds-left}
guarddog-display-name: ''

# 设置魔法牛奶持续多长时间
magic-milk-duration: 25


# ========== 图形界面 ==========

# 材料 of each team in the select team gui
gui-selectteam-teammaterial: white_wool

# The order in which teams will display on the team select GUI
gui-selectteam-sortation: []

# The lore lines of each team material in the select team gui
# Placeholders: {eachplayer}, {players}, {allplayers}, {maxplayers}, {teams}, {maxplayersperteam}
gui-selectteam-teammaterial-lore:
- '&7&m--------------'
- '&7● %Members%'
- '{eachplayer}'
- '&7'
- '&7&a{players}&8/&a{maxplayersperteam}'
- '&7&m--------------'

# Same as the "gui-selectteam-teammaterial-lore" config, only that this one is being disabled when there are no members in the team
gui-selectteam-teammaterial-lore-empty:
- '&7&m--------------'
- '&7● %No_Members%'
- '&7'
- '&7&a{players}&8/&a{maxplayersperteam}'
- '&7&m--------------'

# The {eachplayer} placeholder for the config gui-selectteam-teammaterial-lore
# Adds new lines between the original line that included the {eachplayer} placeholder for each member of the team
# Placeholders: {name}
gui-selectteam-teammaterial-lore-eachplayer: '&7  - &b{name}'

# The background material in the select team gui
gui-selectteam-backgroundmaterial: air

# If the select team gui should be centered
gui-selectteam-centered: true

# The text that will be used for the items within the achievements GUI, varying between if the achievements has been earned or not
# Available placeholders: {name}, {description}, {earn-date}
gui-achievements-text-unearned:
- '&c{name}'
- ' &7&l????'
gui-achievements-text-earned:
- '&a{name}'
- ' &f{description}'
- ''
- '&7&o{earn-date}'

# 材料s that will be used in the achievements GUI, varying between if the achievements has been earned or not
gui-achievements-material-unearned: white_stained_glass_pane
gui-achievements-material-earned: green_terracotta

# The background material in the achievements gui
gui-achievements-backgroundmaterial: air

# If the achievements gui should be centered
gui-achievements-centered: false

# The background material in the spectator teleport gui
gui-spectatortp-backgroundmaterial: air

# If the spectator teleport gui should be centered
gui-spectatortp-centered: false


# ========== 自定义 MOTD ==========

# 如果启用此配置, 此插件将更改 motd
motd-enabled: true

# Type in here the name of your arena to make more multiple placeholders possible
motd-arena: kubo

# How the motd has to look like (motd-enabled has to be true)
# Placeholders: {arena}, {players}, {maxplayers}, {status}, {statusname}
motd-line1: '{statusname}'
motd-line2: ''


# ========== 告示牌 ==========

# Configurations for the antispam at a sign you can create with "/bw spawn joinarenasign <arena>"
sign-antispam-enabled: true
sign-antispam-delay: 1.0

# The text of the lines by the sign you can create with "/bw spawn joinarenasign <arena>"
# Placeholders: {arena}, {status}, {players}, {maxplayers}, {teams}, {teamsize}
sign-line1: '&e&l起床战争'
sign-line2: '&3{arena}'
sign-line3: '{status}'
sign-line4: '&b{players}&e/&b{maxplayers} &e| &b{teams}&ex&b{teamsize}'

# A block will be placed under each sign with the material of 'signblock-material' and the color of 'signblock-color-*' if this config is enabled
signblock-enabled: true

# 材料 of the block
signblock-material: minecraft:white_terracotta

# The color of the block at the specific states
signblock-color-stopped: RED
signblock-color-lobby: GREEN
signblock-color-running: RED
signblock-color-reseting: YELLOW

# The text of the lines of the stats sign which you can create with "/bw spawn rankingsign <place>"
# Placeholders: {player}, {rank}, {kd}, {wl}, {winstreak}, {wins}, {loses}, {kills}, {deaths}, {bedsdestroyed}, {roundsplayed}, {playtime}, {finalkills}
statssign-line1: '&e&l起床战争'
statssign-line2: '&a排名: &f#{rank}'
statssign-line3: '&a&l{player}'
statssign-line4: '&f&l胜利: &a{wins}'


# ========== 记分板 ==========

# 如果禁用此配置, players aren't getting a scoreboard (black box at the right from their screen)
scoreboard-enabled: true

# The look of the heart on the scoreboard during different team states
# The placeholder is available as {heart} in the eachteam line of the scoreboard
# Placeholders: {playersremaining}
scoreboard-heart-alive: '&a&l✓'
scoreboard-heart-bed-gone: '&a{playersremaining}'
scoreboard-heart-dead: '&c&l✘'

# This placeholder is available as {team-indicator} in the eachtime line of the scoreboard. 
# Can be used to indicate the players team
scoreboard-team-indicator-placeholder: '&7你'

# Whether the ingame scoreboard should display empty teams or not
scoreboard-ingame-display-emptyteams: true

# The order in which teams will display on the scoreboard
eachteam-sortation:
- RED
- BLUE
- LIGHT_GREEN
- YELLOW
- CYAN
- WHITE
- PINK
- GRAY


# ========== 聊天 ==========

# 如果启用此配置, 每个标题消息都将在聊天中发送给玩家
# Read more about titles right here: http://minecraft.gamepedia.com/Commands#title
title-inchat: false

# Bukkit plugins listen to so called "events". The order of each plugin is determined by priorities.
# 此配置 changes the priority in which the plugin listens to the chat event.
# 您可以 choose between the priorities: LOWEST, LOW, NORMAL, HIGH, HIGHEST, MONITOR (not recommended)
# LOWEST gets called first, MONITOR the last.
# If you aren't having any conflicts with other plugin (because your e.g. not using a chat plugin), then you should leave this config as it is.
chat-event-listener-priority: HIGH

# 如果启用此配置, 玩家必须写 <teamchat-public-prefix> 才能与其他团队交流
# 聊天 如果团队中只有一个人，聊天前缀将被禁用
teamchat-enabled: true

# What prefix players have to write before their message to chat with other teams
# Examples:
# Hello mates - only visible for team mates
# @hello others! - visible for everyone
teamchat-public-prefix: '@'

# The prefix that will be added to the message to make the people know that he's writing globally
# With "customchatmessage-enabled" set to true: Replaces {public-prefix} placeholder with this one (allowing you to add it anywhere in the message)
# It being set to false: Adds this behind the message written
teamchat-public-prefix-msg: '&6[喊话] '

# Whenever a player sends a message, the plugin is going to hint them once every few days on how write globally
# 消息s that will get send are "TeamChat_Enabled_Hint" and "TeamChat_Disabled_Solo_Hint" (found in the messages file)
teamchat-hint-enabled: true

# 如果启用此配置, 聊天消息按 customchatmessage-message 中指定的方式更改
customchatmessage-enabled: true

# Change the chat-messages (customchatmessage-enabled has to be enabled for this)
# 您可以 use PlaceholderAPI placeholders as well. Note that not all may work due to the chat being an async operation.
# We'd recommend to just use the following placeholders:
#  - {teamcolor} - The color code of the player's team
#  - {public-prefix} - The value of "teamchat-public-prefix-msg" if he wrote globally or empty
#  - {team} - The name of the player's team
#  - {team-initials} - The initials of the player's team
#  - {chat} - Whatever would have been shown before MBedwars would change it (Contains everything, such as the player's name, the message, etc.)
#  - {essentialsgroupmanager} - Players group (Requires EssentialsGroupManager)
#  - {name} - The display name of the player
#  - {message} - The message that has been written
customchatmessage-message: '{public-prefix}{teamcolor}{team} {name}: &f{message}'

# Same as "customchatmessage-message", but while the arena is in the lobby state
# Use "EQUALLY" to use the same message as ingame
customchatmessage-message-lobby: EQUALLY

# What should be used for the {team} placeholder if the player isn't in a team
customchatmessage-message-placeholder-team-none: ✖

# What should be used for the {teamcolor} placeholder if the player isn't in a team
customchatmessage-message-placeholder-teamcolor-none: '&c'

# 如果启用此配置, 自定义聊天消息仅对游戏玩家更改
customchatmessage-onlyfor-players: true

# 如果启用此配置, 观察者的聊天消息按 customchatmessage-message 中指定的方式更改
# Affects even without "customchatmessage-enabled" being enabled
customchatmessage-spectator-enabled: true

# 如果禁用此配置, chat-messages by an spectator will only be visible for other spectators
customchatmessage-spectator-public: false

# Change the chat-messages by a spectator (customchatmessage-spectator-enabled has to be enabled for this)
# 您可以 use PlaceholderAPI placeholders as well. Note that not all may work due to the chat being an async operation.
# We'd recommend to just use the following placeholders:
#  - {chat} - Whatever would have been shown before MBedwars would change it (Contains everything, such as the player's name, the message, etc.)
#  - {essentialsgroupmanager} - Players group (Requires EssentialsGroupManager)
#  - {name} - The display name of the player
#  - {message} - The message that has been written
customchatmessage-spectator-message: '&7[旁观者] {name}: &f{message}'

# 如果启用此配置, 非游戏玩家的消息对游戏玩家不可见
chat-others-unvisible: true

# 如果启用 then messages by playing players will be hidden for players who aren't playing (except spectators) in the same arena
chat-playing-private: true


# ========== 全息图和实体类型 ==========

# The name of the dealer you can spawn with "/bw spawn dealer" or within "/bw arena setupgui"
# It's possible to have multiple lines by simply splitting the lines through a \n
# Note that "/bw reload" won't have any effect. You must restart your server after a change
dealer-title: '&e&l物品商店\n&7&n右键打开'
upgradedealer-title: '&a&l团队商店\n&7&n右键打开'

# Allow players to left click on the dealer to open the shop, by default only right click is permitted.
# This is always enabled for bedrock players for compatibility reasons.
dealer-left-click-openshop: false

# Whether or not it should open the shop when you click on a villager that you didn't spawn with "/bw spawn dealer"
villager-interact-openshop: true

# Run a command when you interact a villager that you did not spawn with "/bw spawn dealer". Leave it empty to disable
# Placeholders: {player}, {playeruuid}, {entityid}
villager-interact-runcommand: ''

# True: The console will execute 'villager-interact-runcommand'
# False: It'll act like if the player wrote the command
villager-interact-runcommand-asop: false

# 您可以 change the hologram types for the dealer, hub and teamselect here.
# Wiki entry: https://s.marcely.de/mbww4
# 您可以 选择：
#  - Villager
#  - NPC[<UUID from owner of skin>]
#  - NPC[self]
#  - ArmorStand{<Parameters>}
entitytype-dealer: Villager
entitytype-arenasguistatue: Villager
entitytype-teamselect: Villager
entitytype-upgradedealer: Villager

# The vertical spacing factor between the hologram title lines.
# This applies to stats holos, dealers, spawners, and more.
hologram-title-vertical-spacings: 1.0


# ========== 占位符 ==========

# This string will be used for the provided PAPI placeholders whenever the player is not inside an arena
placeholderapi-not-inside-arena: ''

# Create custom mode placeholders to how many players are in certain arenas
# Example: Config -> 'bigArena: [teams=12]' PAPI Placeholder -> '%mbedwars_players-in-mode-bigArena%'
# Read more about arena pickers: https://s.marcely.de/mbww15
player-picker-placeholder:
  all: '[]'
  quads: '[players_per_team=4]'
  doubles: '[players_per_team=2]'
  trios: '[players_per_team=3]'
  solos: '[players_per_team=1]'

# Create custom mode placeholders to how many players are in certain arenas
# Example: Config -> 'bigArena: [teams=12]' PAPI Placeholder -> '%mbedwars_players-in-mode-bigArena%'
# Read more about arena pickers: https://s.marcely.de/mbww15
# The format of the dates which are being used
dateformat: MM.dd.yyyy

# The value that %mbedwars_playerarena-current-team-color% returns if a player does not have a team
placeholderapi-no-team-color: ''

# How player names should be formatted throughout the plugin
# AUTO - Returns the player nick if one exists, otherwise returns the player's name
# DISPLAY_NAME - Returns the player's display name
# REAL_NAME - Returns the player's unformatted name
players-public-displayed-name: AUTO

# The ip for the placeholder {ip} in the scoreboard
ip-display: Mc244.Com

# Allows you to display all of the teams, in the current arena, in a horizontal line.
# To use this feature, simply add this placeholder into the ingame scoreboard config file: {teamsleft}
# Placeholders: {team}, {teamcolor} {playersremaining}
scoreboard-ingame-teamsleft: '{teamcolor}■'
scoreboard-ingame-teamsleft-bed-gone: '{teamcolor}{playersremaining}'
scoreboard-ingame-teamsleft-dead: '{teamcolor}X'


# ========== 插件：PvPLevels ==========

# 启用此功能将 如果玩家做了特殊的事情（如破坏床），给玩家指定数量的经验
# 当然需要插件 PvPLevels
pvplevels-enabled: true

# 数量 exp that'll be given for doing an event
pvplevels-exp-win: 100
pvplevels-exp-lose: 60
pvplevels-exp-beddestroy: 20
pvplevels-exp-killplayer: 5
pvplevels-exp-killplayer-final: 7
pvplevels-exp-killplayer-lastinteam: 8


# ========== 插件：DKCoins/NickAPI（硬币）==========

# 启用此功能将 如果玩家做了特殊的事情（如破坏床），给玩家指定数量的硬币
# 显然需要安装 DKCoins 或 NickAPI
coins-enabled: true

# 数量 coins that'll be given for doing an event
coins-give-win: 100
coins-give-lose: 60
coins-give-beddestroy: 20
coins-give-killplayer: 5
coins-give-killplayer-final: 7
coins-give-killplayer-lastinteam: 8


# ========== 插件：Parties/PaF 等 ==========

# 如果队长加入竞技场，让队伍成员跟随他们的队长
# 这适用于任何具有与此插件挂钩的队伍系统的插件
# 更多信息请参见 wiki：https://s.marcely.de/mbww18
parties-member-follow-enabled: true


# ========== AUTOMATIC JOIN ==========

# 如果启用此配置, 如果人们加入服务器，他们将自动加入竞技场。
# IMPORTANT: It's NOT recommended to use this with BungeeCord. We recommend you to use the BungeeCord Addon!
autojoin-enabled: true

# Specify the arena they will enter (autojoin-enabled has to be enabled for this)
# Did you enable "voting" and would like to make people automatically spectate the active match?
# Or did you enable "voting" and would like to auto-start a new arena with your active Cloud System?
# Then you might want to insert the following: %best_prefer_non_voting[status=3 | type="voting"]%
# Read more about arena pickers: https://s.marcely.de/mbww15
autojoin-arena: kubo

# 启用此功能 如果您实际上不想让玩家自动加入竞技场，
# 而是只想使用下面的配置将他送回
# 如果此配置设置为 true，则配置 "autojoin-arena" 无用
autojoin-only-send-back: false

# 指定玩家应该如何被送回的方式。
# 您可以 在 'Kick'、'BungeeCord' 和 'Nothing' 之间选择。
autojoin-way: BungeeCord

# 如果您在上面选择了 BungeeCord，请确保填写此项。
# 记住：这不适用于 BungeeCord 附加组件
autojoin-way-bungeecord-lobby: BedwarsHub#1


# ========== CLOUDSYSTEM ==========

# 如果启用配置，此插件将把在配置 'cloudsystem-arena' 中指定的竞技场数据发送到 CloudSystem
# 当前支持的 CloudSystems：CloudSystemIO、CaveCloud、CloudNetV2、CloudNetV3、TimoCloud、ReformCloud
# Wiki 页面：https://s.marcely.de/mbww6
cloudsystem-enabled: false

# 在此指定应发送到 CloudSystem 的竞技场
cloudsystem-arena: ''

# 额外字段应该是什么。
# 如果配置没有额外字段，motd 将使用额外字段设置。
# 您可以 选择：
#  - NONE
#  - ARENA
#  - TEAMS_X_TEAMPLAYERS
cloudsystem-extra: ARENA


# ========== STORAGE ==========

# 定义玩家数据（和一些通用数据）应该存储在哪里
# 可行的选项有：
# - LOCAL
# - MYSQL
# - MARIADB
# - MICROSOFT_SQL
# - MONGODB
# 请注意，您始终可以使用 "/bw tools migrate service" 命令进行迁移
storage-type: MYSQL

# 您的 SQL 数据库设置
sql-host: localhost
sql-port: 3306
sql-database: bedwarsnew
sql-user: bedwarsnew
sql-password: 6h2mF5NTL6newBxY

# 除非您真的知道自己在做什么，否则不要更改它。如果不谨慎，可能会出现问题
# 此配置 对 MICROSOFT_SQL 无效
sql-parameters: ?useUnicode=yes&characterEncoding=UTF-8&allowMultiQueries=true&serverTimezone=UTC

# 这些配置专用于 MICROSOFT_SQL
sql-mssql-encrypt: true
sql-mssql-integrated-security: false

# 您的 MongoDB 数据库设置
mongodb-host: localhost
mongodb-port: 27017
mongodb-use-ssl: false
mongodb-database: mbedwars
mongodb-authenticate: false
mongodb-authdb: admin
mongodb-user: root
mongodb-password: '123456'
