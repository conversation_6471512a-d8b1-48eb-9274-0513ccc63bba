#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Config.yml 翻译脚本 - 将配置文件中的英文注释翻译为中文
"""

import re

def translate_config():
    """翻译 config.yml 文件"""
    
    # 读取原文件
    with open('config.yml', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 备份原文件
    with open('config.yml.backup', 'w', encoding='utf-8') as f:
        f.write(content)
    
    # 翻译映射表 - 注释翻译
    comment_translations = {
        # 基础配置
        "# The messages, which the plugin is sending to the player, will be taken of that file (located in the 'languages' folder)": "# 插件发送给玩家的消息将从该文件中获取（位于 'languages' 文件夹中）",
        "# If 'language-per-player' is also enabled then this language is also automatically the default language for players whose language hasn't been created": "# 如果同时启用了 'language-per-player'，那么这种语言也会自动成为尚未创建语言的玩家的默认语言",
        "# Enabling this will cause the plugin to load every messages file of the 'folder'": "# 启用此功能将导致插件加载 'folder' 中的每个消息文件",
        "# The plugin will then automatically send the message in the language of the player": "# 然后插件将自动以玩家的语言发送消息",
        "# The RAM usage will increase as every message from every language has to be kept in memory": "# RAM 使用量会增加，因为每种语言的每条消息都必须保存在内存中",
        "# A few messages, such as those in the scoreboard, may remain in the same language as 'language-file'": "# 一些消息，如记分板中的消息，可能仍然使用与 'language-file' 相同的语言",
        "# An addition to \"language-per-user\": Players require the permission mbedwars.langperuser for it to work": "# \"language-per-user\" 的补充：玩家需要 mbedwars.langperuser 权限才能使其工作",
        "# \"language-per-user\" must be set to true as well when using this feature": "# 使用此功能时，\"language-per-user\" 也必须设置为 true",
        "# If this config is enabled then only people with the permission 'mbedwars.beta' will be able to join": "# 如果启用此配置，则只有拥有 'mbedwars.beta' 权限的人才能加入",
        "# If this config is enabled then the world's time will be fixed to (day light)": "# 如果启用此配置，则世界时间将固定为（白天）",
        "# This effect is only noticeable for playing players and spectators": "# 此效果仅对游戏玩家和观察者可见",
        "# NOTE: Time can be individually set per arena. This will define the default behavior for new arenas.": "# 注意：时间可以为每个竞技场单独设置。这将定义新竞技场的默认行为。",
        "# If this config is enabled then the world won't rain for playing players and spectators anymore": "# 如果启用此配置，则世界不会再为游戏玩家和观察者下雨",
        "# NOTE: Weather can be individually set per arena. This will define the default behavior for new arenas.": "# 注意：天气可以为每个竞技场单独设置。这将定义新竞技场的默认行为。",
        "# Enabling this config causes the plugin to save the players inventory before they enter an arena": "# 启用此配置会导致插件在玩家进入竞技场之前保存玩家的物品栏",
        "# Once they leave the arena their inventory will be replaced by the saved one": "# 一旦他们离开竞技场，他们的物品栏将被保存的物品栏替换",
        "# If disabled then their inventory won't change causing them to keep the items they got from bedwars": "# 如果禁用，则他们的物品栏不会改变，导致他们保留从起床战争中获得的物品",
        "# Because of that it's not recommended to disable it, unless you decide to e.g. use \"inventory-clear\" or don't want the plugin to replace the items from your hub": "# 因此不建议禁用它，除非您决定例如使用 \"inventory-clear\" 或不希望插件替换来自您大厅的物品",
        "# Enable/disable if it shall clear the players inventory after he left an arena": "# 启用/禁用是否在玩家离开竞技场后清除玩家的物品栏",
        "# This config has no effect when \"inventory-backup\" is enabled": "# 当启用 \"inventory-backup\" 时，此配置无效",
        "# If this config is enabled, people can't change their gamemode to creative or enable flying": "# 如果启用此配置，人们无法将游戏模式更改为创造模式或启用飞行",
        "# Setting this to false causes the \"player x left the arena\" message to not getting displayed on final death (forcefully left because he got destroyed)": "# 将此设置为 false 会导致在最终死亡时不显示 \"玩家 x 离开了竞技场\" 消息（因为被摧毁而被迫离开）",
        "# Players will leave the round if they're using one of these commands": "# 如果玩家使用这些命令之一，他们将离开回合",
        "# Decide whether you agree with sharing certain details that may be used for statistics of global interest": "# 决定您是否同意共享某些可能用于全球统计的详细信息",
        "# We use them to identify the aspects on which we should put more focus on (such as bugs etc) and to possibly share them with the community": "# 我们使用它们来识别我们应该更多关注的方面（如错误等），并可能与社区分享",
        "# Note that disabling this config won't disable the update checker and the depdendency downloader, as they are required for the general functionality of the plugin": "# 请注意，禁用此配置不会禁用更新检查器和依赖项下载器，因为它们是插件一般功能所必需的",
        "# Whether or not a player alone on a team (with a bed) is able to rejoin a game if they get disconnected/leave": "# 团队中的单独玩家（有床）是否能够在断开连接/离开时重新加入游戏",
        "# How many seconds a solo player has to rejoin before their bed will automatically be destroyed, and their team eliminated.": "# 单独玩家在床被自动摧毁和团队被淘汰之前有多少秒重新加入。",
        "# If this config is enabled, the player's name will be colored (works via packets (you won't see the color of players if they're in an other team))": "# 如果启用此配置，玩家的名字将被着色（通过数据包工作（如果他们在其他团队中，您将看不到玩家的颜色））",
        "# The server will automatically restart itself if this config is enabled and when an arena ends": "# 如果启用此配置并且当竞技场结束时，服务器将自动重启",
        "# Players will get kicked if they're teleporting themselves more than 12 blocks away from their location while they're in a playing round": "# 如果玩家在游戏回合中将自己传送到距离其位置超过 12 个方块的地方，他们将被踢出",
        "# Admins will still be permitted to teleport": "# 管理员仍然被允许传送",
        "# Players will get kicked if they're teleporting themself within a lobby": "# 如果玩家在大厅内传送自己，他们将被踢出",
        "# Disabling this will remove the message which is coming when a player is entering or leaving the server": "# 禁用此功能将删除玩家进入或离开服务器时出现的消息",
        "# If true, this plugin won't make any changes to these messages": "# 如果为 true，此插件不会对这些消息进行任何更改",
        "# Setting this to false will (almost) completely disable the use of actionbar messages": "# 将此设置为 false 将（几乎）完全禁用操作栏消息的使用",
        "# The message which will be displayed in the actionbar": "# 将在操作栏中显示的消息",
        "# \"actionbar-enabled\" must be set to true for this": "# 为此，\"actionbar-enabled\" 必须设置为 true",
        "# Placeholders: {team-color}, {team-display-name}, {team-players}, {players-per-team}": "# 占位符：{team-color}, {team-display-name}, {team-players}, {players-per-team}",
        "# Will hide players that aren't in the round from playing players": "# 将向游戏玩家隐藏不在回合中的玩家",
        "# Spectators are also getting hidden": "# 观察者也会被隐藏",
        "# Spectators will only see themself and players inside the round": "# 观察者只会看到自己和回合内的玩家",
        "# Special case if \"endlobby-tptolobby\" is enabled: Spectators will become visible after the round has ended": "# 特殊情况，如果启用了 \"endlobby-tptolobby\"：观察者将在回合结束后变为可见",
        "# Will hide playing players and spectators for non-playing players": "# 将为非游戏玩家隐藏游戏玩家和观察者",
        "# Nobody will see spectators when this is enabled": "# 启用此功能时，没有人会看到观察者",
        "# With this config set to false, no mobs (animals and monsters) are going to spawn within the arenas": "# 将此配置设置为 false 时，竞技场内不会生成生物（动物和怪物）",
        "# Most plugins shouldn't be affected by this": "# 大多数插件不应受此影响",
        "# Define the cases in which it's okay that a mob spawns": "# 定义生物生成可以接受的情况",
        "# Complete list: https://hub.spigotmc.org/javadocs/spigot/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html": "# 完整列表：https://hub.spigotmc.org/javadocs/spigot/org/bukkit/event/entity/CreatureSpawnEvent.SpawnReason.html",
        "# Note that not all spawn reasons exist in older versions": "# 请注意，并非所有生成原因都存在于旧版本中",
        "# This config is required if you are using upgrades, as it sets the radius of a base's size": "# 如果您使用升级，则需要此配置，因为它设置基地大小的半径",
        "# Center points are both the bed's position and the team spawn point, everything in either of their radius will be considered as part of the base": "# 中心点既是床的位置也是团队生成点，在它们任一半径内的所有内容都将被视为基地的一部分",
        "# This is the radius, not diameter! And it has a circular shape, not cubic": "# 这是半径，不是直径！它具有圆形形状，不是立方体",
        "# If this config is disabled then the hunger level of the players won't change (Only ingame)": "# 如果禁用此配置，则玩家的饥饿等级不会改变（仅在游戏中）",
        "# Increasing this value will increase the amount of the damage players will take from falling": "# 增加此值将增加玩家从跌落中受到的伤害量",
        "# Disabling this causes the player to not being able to interact with specific materials": "# 禁用此功能会导致玩家无法与特定材料交互",
        "# These include: crafting table, anvil, dropper, dispenser, furnace, beacon, hopper, enchanting table,": "# 这些包括：工作台、铁砧、投掷器、发射器、熔炉、信标、漏斗、附魔台、",
        "#   fletching table, smoker, blast furnace, stonecutter, smithing table, cartography table, brewing stands,": "#   制箭台、烟熏炉、高炉、切石机、锻造台、制图台、酿造台、",
        "#   signs, and flower pots": "#   告示牌和花盆",
        "# The message that'll be sent when a player is receiving an achievement": "# 玩家获得成就时将发送的消息",
        "# Placeholders: {name}, {description}": "# 占位符：{name}, {description}",
        "# Disable/enable the auto teambalance in the lobby": "# 禁用/启用大厅中的自动团队平衡",
        "# It tries to make the teams as fair as possible": "# 它试图使团队尽可能公平",
        "# Warning: Disabling this will make it possible for all players to join the same, single team (thereby having no enemies)": "# 警告：禁用此功能将使所有玩家都可以加入同一个团队（从而没有敌人）",
        "# It is only recommended to disable this if you have your own implementation or play with people you trust": "# 只有在您有自己的实现或与您信任的人一起玩时，才建议禁用此功能",
        "# If this config is enabled then the configs which start with 'giveitems-on-' will work": "# 如果启用此配置，则以 'giveitems-on-' 开头的配置将起作用",
        "# Specify here which items should be given on round start": "# 在此指定回合开始时应给予哪些物品",
        "# 'giveitems-on-enabled' has to be enabled for that!": "# 为此必须启用 'giveitems-on-enabled'！",
        "# Specify here which armor items should be set to the inventory slots on round start": "# 在此指定回合开始时应将哪些盔甲物品设置到物品栏槽位",
        "# Specify here which items should be given on respawn": "# 在此指定重生时应给予哪些物品",
        "# Specify here which armor items should be set to the inventory slots on respawn": "# 在此指定重生时应将哪些盔甲物品设置到物品栏槽位",
        "# Specify here which potion effects the player shall gain after he respawns": "# 在此指定玩家重生后应获得哪些药水效果",
        "# \"giveitems-on-enabled\" has no effect to this": "# \"giveitems-on-enabled\" 对此无效",
        "# Usage: <potion effect name>:<duration in ticks (20 ticks = 1 sec):<level>": "# 用法：<药水效果名称>:<持续时间（刻）（20 刻 = 1 秒）>:<等级>",
        "# If false, players won't be able to move or drop the armor in their inventory": "# 如果为 false，玩家将无法移动或丢弃其物品栏中的盔甲",
        "# This command gets executed when a round starts": "# 此命令在回合开始时执行",
        "# Configure the way commands added to \"blocked-commands\" will be treated": "# 配置添加到 \"blocked-commands\" 的命令的处理方式",
        "# You may choose between:": "# 您可以选择：",
        "#  - DISABLED: The config has no effect": "#  - DISABLED：配置无效",
        "#  - BLACKLIST: The specific commands will be permitted": "#  - BLACKLIST：特定命令将被禁止",
        "#  - WHITELIST: All commands, apart from these, will be blocked": "#  - WHITELIST：除这些命令外，所有命令都将被阻止",
        "# Admins may bypass restrictions by having the permission \"mbedwars.bypassblockedcommands\"": "# 管理员可以通过拥有 \"mbedwars.bypassblockedcommands\" 权限来绕过限制",
        "# These commands will be blocked / permitted for players and spectators": "# 这些命令将被阻止/允许玩家和观察者使用",
        "# Configure the exact behaviour using the \"blocked-commands-mode\" config": "# 使用 \"blocked-commands-mode\" 配置来配置确切的行为",
        "# Syntax: /<command>": "# 语法：/<命令>",
        "# Example: /tpa": "# 示例：/tpa",
        "#  -> This will block all variants of \"/tpa\", including \"/TpA\" or \"/tpa player123\"": "#  -> 这将阻止 \"/tpa\" 的所有变体，包括 \"/TpA\" 或 \"/tpa player123\"",
        "# The armor is naturally visible when having the invisibility potion effect": "# 拥有隐身药水效果时，盔甲自然可见",
        "# This config allows you to hide the players armor when set to true": "# 此配置允许您在设置为 true 时隐藏玩家的盔甲",
        "# If this config is enabled, the particles of the invisibility potion effect will be removed": "# 如果启用此配置，隐身药水效果的粒子将被移除",
        "# The config \"invisibility-hides-armor\" has to be enabled for this to work": "# 配置 \"invisibility-hides-armor\" 必须启用才能使其工作",
        "# You may spawn further potion particles while a player is invisible": "# 您可以在玩家隐身时生成更多药水粒子",
        "# The greater the value, the more particles are being spawned": "# 值越大，生成的粒子越多",
        "# The config \"invisibility-hides_armor_remove-particles\" has to be enabled and \"invisibility-hides-armor\" has to be disabled for this to work": "# 配置 \"invisibility-hides_armor_remove-particles\" 必须启用，\"invisibility-hides-armor\" 必须禁用才能使其工作",
        "# Team chests can be opened by all players of the team, from any chest of the configured type inside the arena": "# 团队箱子可以被团队的所有玩家打开，从竞技场内配置类型的任何箱子",
        "# Some people may prefer to use regular chests as team chests": "# 有些人可能更喜欢使用普通箱子作为团队箱子",
        "# Specify which chest variant shall be used as the team chest": "# 指定应将哪种箱子变体用作团队箱子",
        "# You may decide between ENDER_CHEST and CHEST": "# 您可以在 ENDER_CHEST 和 CHEST 之间选择",
        "# If true, players will not be able to purchase items from the shop outside of the arena": "# 如果为 true，玩家将无法在竞技场外从商店购买物品",
        "# This could be useful for allowing players to edit their QuickBuy in lobbies": "# 这对于允许玩家在大厅中编辑他们的快速购买可能很有用",
        "# By default, if an item will not fit into a players inventory, it will drop on the ground next to them": "# 默认情况下，如果物品不适合玩家的物品栏，它将掉落在他们旁边的地面上",
        "# This will prevent players from purchasing items that would not fit in their inventory": "# 这将阻止玩家购买不适合其物品栏的物品",
        "# [FOR DEVS] Define whether MBedwars should respect other plugin's event cancellations": "# [开发者] 定义 MBedwars 是否应该尊重其他插件的事件取消",
        "# The events that are being affected by this config are: BlockPlace, BlockBreak, EntityDamage, EntityDamageByEntity": "# 受此配置影响的事件有：BlockPlace、BlockBreak、EntityDamage、EntityDamageByEntity",
        "# If this is set to true, then the plugin will not execute any actions if one of the events has been cancelled by another plugin": "# 如果设置为 true，则如果其中一个事件被另一个插件取消，插件将不会执行任何操作",
        "# It is recommended to keep this at false, as it may otherwise cause unexpected behaviour": "# 建议将此保持为 false，否则可能导致意外行为",
        "# One purpose of this config is to fix potential conflicts with anti cheats": "# 此配置的一个目的是修复与反作弊的潜在冲突",

        # LOBBY 部分
        "# ========== LOBBY ==========": "# ========== 大厅 ==========",
        "# The countdown time that should be set when using forcestart": "# 使用强制开始时应设置的倒计时时间",
        "# false: The force-start lobby item and the command \"/bw forcestart\" requires the minimum players count to be fulfilled": "# false：强制开始大厅物品和命令 \"/bw forcestart\" 需要满足最少玩家数量",
        "# true: The force-start lobby item and the command \"/bw forcestart\" may be used, even if they are the only ones in the arena": "# true：强制开始大厅物品和命令 \"/bw forcestart\" 可以使用，即使他们是竞技场中唯一的人",
        "# This calculation is being used when the countdown in the lobby is starting.": "# 当大厅中的倒计时开始时使用此计算。",
        "# Changing this value to low is dangerous.": "# 将此值更改为低值是危险的。",
        "# Placeholder: {teams}, {teamplayers}": "# 占位符：{teams}, {teamplayers}",
        "# How many seconds a player has to wait before before being sent back to the lobby after": "# 玩家在被送回大厅之前必须等待多少秒",
        "# clicking the leave item. They can cancel the leave by clicking again in this time.": "# 点击离开物品。他们可以在此时间内再次点击来取消离开。",
        "# Set to 0 to make it instant": "# 设置为 0 使其立即生效",
        "# Automatically shorten the countdown when the percentage of players in the arena is reached": "# 当达到竞技场中玩家的百分比时自动缩短倒计时",
        "# This feature is useful to reduce the wait time when there are enough players in the arena": "# 当竞技场中有足够的玩家时，此功能有助于减少等待时间",
        "# Set it to 101 to disable this feature": "# 将其设置为 101 以禁用此功能",
        "# Related to \"auto-shorten-countdown-percent\"": "# 与 \"auto-shorten-countdown-percent\" 相关",
        "# This is the time in seconds that the countdown will be shortened to when the percentage of players is reached": "# 这是当达到玩家百分比时倒计时将缩短到的时间（以秒为单位）",
        "# If true, particle animations will be played when a player joins or leaves an arenas lobby": "# 如果为 true，当玩家加入或离开竞技场大厅时将播放粒子动画",
        "# The expbar is being animated in case this config is enabled": "# 如果启用此配置，经验条将被动画化",
        "# Animations include the remaining time slowly decreasing on the exp bar": "# 动画包括经验条上剩余时间的缓慢减少",
        "# and the level displaying the amount of seconds left to start": "# 以及显示开始前剩余秒数的等级",
        "# If this config is enabled, the lines by the config 'lobby-printmapinfo-lines' will be printed in the lobby in the 10th second": "# 如果启用此配置，配置 'lobby-printmapinfo-lines' 的行将在第 10 秒在大厅中打印",
        "# The lines which will be printed before the game begins": "# 游戏开始前将打印的行",
        "# Placeholder: {arena}, {madeby}, {players}, {maxplayers}, {teams}, {teamsize}": "# 占位符：{arena}, {madeby}, {players}, {maxplayers}, {teams}, {teamsize}",
        "# You can use ^# to create space": "# 您可以使用 ^# 来创建空间",
        "# This message will be displayed at the same time as the printmapinfo": "# 此消息将与 printmapinfo 同时显示",
        "# Placeholder: {arena}, {players}, {maxplayers}, {author}": "# 占位符：{arena}, {players}, {maxplayers}, {author}",
        "# These titles which will be displayed at the given second until the game ends": "# 这些标题将在给定的秒数内显示，直到游戏结束",
        "# If this config is enabled and if a player is changing than he will wear the dyed item 'lobby-team-onchange-wearcloth-material'": "# 如果启用此配置并且玩家正在更改，那么他将穿着染色物品 'lobby-team-onchange-wearcloth-material'",
        "# The armor that the player will wear": "# 玩家将穿着的盔甲",
        "# 'lobby-team-onchange-wearcloth-enabled' has to be enabled for this": "# 为此必须启用 'lobby-team-onchange-wearcloth-enabled'",
        "# If the stats should be shown in the endlobby": "# 是否应在结束大厅中显示统计信息",
        "# If the time until the player is getting kicked should be shown in the endlobby": "# 是否应在结束大厅中显示玩家被踢出前的时间",
        "# How long the players should remain in the endlobby state until they get kicked out": "# 玩家应在结束大厅状态中保持多长时间，直到他们被踢出",
        "# Making the value of this config is dangerous and can cause unexpected and unwanted problems": "# 更改此配置的值是危险的，可能导致意外和不需要的问题",
        "# Whether or not players are able to fly in endlobby": "# 玩家是否能够在结束大厅中飞行",
        "# Setting this to true causes all remaining players and spectators to get teleported back to the lobby during the endlobby phase": "# 将此设置为 true 会导致所有剩余玩家和观察者在结束大厅阶段被传送回大厅",
        "# By this their inventory gets cleared up, spectators become visible again, etc.": "# 通过这种方式，他们的物品栏被清理，观察者再次变为可见等。",
        "# If set to false then the players and spectators are staying where they already are and don't get moved back": "# 如果设置为 false，则玩家和观察者停留在他们已经在的地方，不会被移回",
        "# This set to false can be useful when using the lobbybreak feature": "# 当使用 lobbybreak 功能时，将此设置为 false 可能很有用",
        "# If the lobby should get destroyed after the arena starts": "# 竞技场开始后是否应摧毁大厅",
        "# NOTE: If your lobby is outside of your arena, this config will have no effect": "# 注意：如果您的大厅在竞技场外，此配置将无效",
        "# The radius at which the blocks will get destroyed with the lobby point being at the middle": "# 以大厅点为中心，方块将被摧毁的半径",
        "# 'lobbybreak-enabled' has to be enabled for this": "# 为此必须启用 'lobbybreak-enabled'",

        # ARENA VOTING 部分
        "# ========== ARENA VOTING ==========": "# ========== 竞技场投票 ==========",
        "# Set the maximum amount of arenas which should get voted": "# 设置应投票的竞技场的最大数量",

        # AUTOMATED ARENA CLONING 部分
        "# ========== AUTOMATED ARENA CLONING ==========": "# ========== 自动竞技场克隆 ==========",
        "# This feature *automatically* creates duplicates of arenas, whereby there are always arenas available for one to join.": "# 此功能*自动*创建竞技场的副本，从而始终有可供加入的竞技场。",
        "# After a cloned arena has gone through a match, it will be deleted and a new one is being generated. It is safe to disable this config later on, trashed worlds will be cleaned up automatically.": "# 克隆的竞技场经过比赛后，它将被删除并生成一个新的。稍后禁用此配置是安全的，垃圾世界将自动清理。",
        "# It works with both REGION and WORLD arenas:": "# 它适用于 REGION 和 WORLD 竞技场：",
        "# - In case your arena is a WORLD, a new world is being temporarily created while the arena exists. This occurs with every cloned arena, meaning that you might experience a large increase in worlds count and RAM usage": "# - 如果您的竞技场是 WORLD，则在竞技场存在时会临时创建一个新世界。这发生在每个克隆的竞技场中，意味着您可能会遇到世界数量和 RAM 使用量的大幅增加",
        "# - If your arena is instead a REGION, all cloned match areas are being collected in a single world. There's an offset with each one, meaning that they won't collide with each other": "# - 如果您的竞技场是 REGION，则所有克隆的比赛区域都收集在一个世界中。每个都有偏移，意味着它们不会相互碰撞",
        "# One thing regarding arena's lobby: It is recommended for them to be inside the match area, otherwise they won't be moved for cloned arenas.": "# 关于竞技场大厅的一件事：建议它们在比赛区域内，否则它们不会为克隆的竞技场移动。",
        "# If the lobby is outside of the match area, players joining the same kind of arenas might see each other.": "# 如果大厅在比赛区域外，加入同类竞技场的玩家可能会看到彼此。",
        "# In this case, it *might* make sense to enable \"tab-removenonplayers\" as the players from the other arenas won't be seen with that.": "# 在这种情况下，启用 \"tab-removenonplayers\" *可能*有意义，因为来自其他竞技场的玩家不会被看到。",
        "# Define the names of the arenas that shall be excluded from auto cloning": "# 定义应从自动克隆中排除的竞技场名称",
        "# Amount of cloned arenas that are waiting for players (arenas in LOBBY state) shall be provided for each arena": "# 应为每个竞技场提供等待玩家的克隆竞技场数量（处于 LOBBY 状态的竞技场）",
        "# Related to config \"auto-cloning_lobby-arenas-count\"": "# 与配置 \"auto-cloning_lobby-arenas-count\" 相关",
        "# Setting this to true results to new cloned arenas being provided once arenas in LOBBY state are full": "# 将此设置为 true 会导致一旦处于 LOBBY 状态的竞技场满员就提供新的克隆竞技场",
        "# Which modes (players per team) shall be provided for an arena type": "# 应为竞技场类型提供哪些模式（每队玩家数）",
        "# Basically, you may auto assign certain players per team for cloned arenas": "# 基本上，您可以为克隆的竞技场自动分配每队的某些玩家",
        "# If a mode is not specified for an arena, that arena will only be cloned to the same player count": "# 如果没有为竞技场指定模式，该竞技场将只克隆到相同的玩家数量",
        "# It only makes sense to have the same amount or less modes as specified within the \"auto-cloning-lobby-arenas-count\" config": "# 只有拥有与 \"auto-cloning-lobby-arenas-count\" 配置中指定的相同数量或更少的模式才有意义",
        "# Left side: Arena picker (see https://s.marcely.de/mbww15). Define the arenas for which the modes apply to": "# 左侧：竞技场选择器（参见 https://s.marcely.de/mbww15）。定义模式适用的竞技场",
        "# Right side: The modes that shall be provided for the given arenas": "# 右侧：应为给定竞技场提供的模式",
        "# Amount of clones of an arena that may exist during the same time": "# 同时可能存在的竞技场克隆数量",
        "# Use -1 to not have any limit": "# 使用 -1 表示没有任何限制",
        "# The prefix which the newly created worlds shall have": "# 新创建的世界应具有的前缀",
        "# It is not recommended to change this after auto-cloning has been enabled. Or better yet, keep this config as it is": "# 不建议在启用自动克隆后更改此设置。或者更好的是，保持此配置不变",
        "# It is very important that you understand that these worlds are being deleted automatically and that you may lose something when acting careless": "# 非常重要的是，您要理解这些世界会被自动删除，如果不小心操作，您可能会丢失某些东西",

        # BED 部分
        "# ========== BED ==========": "# ========== 床 ==========",
        "# If this config is enabled, players can destroy the bed of their team": "# 如果启用此配置，玩家可以摧毁他们团队的床",
        "# Set the block that should be destroyed in a round": "# 设置在回合中应被摧毁的方块",
        "# The plugin will automatically dye the beds to the corresponding team's color when enabled": "# 启用时，插件将自动将床染成相应团队的颜色",
        "# The itemspawner that'll be dropped when a player is destroying a bed": "# 玩家摧毁床时将掉落的物品生成器",
        "# The amount of the materials that will be dropped once a player destroyed a bed (it'll take the material of the spawner by the config 'bed-drops-type')": "# 玩家摧毁床后将掉落的材料数量（它将采用配置 'bed-drops-type' 的生成器材料）",
        "# If it's possible to break the bed with TNT or other kinds of explosions (like a fireball, TNT Sheep etc.)": "# 是否可以用 TNT 或其他类型的爆炸（如火球、TNT 羊等）破坏床",
        "# Enabling this config will it make so that players will not be able to break beds by hand": "# 启用此配置将使玩家无法用手破坏床",
        "# They then MUST use TNT or something that causes an explosion (like a fireball) to break it": "# 然后他们必须使用 TNT 或引起爆炸的东西（如火球）来破坏它",
        "# Make sure \"bed-destroyableby-tnt\" is set to true, otherwise this config won't have any effect": "# 确保 \"bed-destroyableby-tnt\" 设置为 true，否则此配置不会有任何效果",
        "# Enabling this config will spawn a hologram above every living bed with the message of the config 'bed-hologram-message-alive'": "# 启用此配置将在每个活着的床上方生成一个全息图，显示配置 'bed-hologram-message-alive' 的消息",
        "# The message which is being displayed as a hologram above the bed when it's not broken. 'bed-hologram-enabled' has to be enabled for this!": "# 当床没有被破坏时，作为全息图显示在床上方的消息。为此必须启用 'bed-hologram-enabled'！",
        "# Placeholders: {teamcolor}, {team}, {heart}": "# 占位符：{teamcolor}, {team}, {heart}",
        "# Disabling this will cause that the player won't be able to interact with the bed block which you've placed during set-up": "# 禁用此功能将导致玩家无法与您在设置期间放置的床方块交互",
        "# This will cause the 'You can only sleep at night' message to not appear": "# 这将导致 '您只能在夜间睡觉' 消息不出现",

        # SPAWNERS 部分
        "# ========== SPAWNERS ==========": "# ========== 生成器 ==========",
        "# If this config is enabled, iron and gold will make particles once they spawn": "# 如果启用此配置，铁和金在生成时会产生粒子",
        "# If this config is enabled, iron and gold will make a sound when they spawn": "# 如果启用此配置，铁和金在生成时会发出声音",
        "# If this config is enabled, all itemspawners will automatically center in the block you place them in.": "# 如果启用此配置，所有物品生成器将自动在您放置它们的方块中居中。",
        "# Set this to false if you want to be able to set an exact position.": "# 如果您想要能够设置确切位置，请将此设置为 false。",
        "# The height of the item spawner holograms": "# 物品生成器全息图的高度",
        "# The animation speed of the item spawner holograms": "# 物品生成器全息图的动画速度",
        "# Enabling this will cause the holograms of the spawners that are located within the players base to get hidden": "# 启用此功能将导致位于玩家基地内的生成器的全息图被隐藏",
        "# Use the config \"upgrade-spawnsize\" to specify the radius of the team spawn": "# 使用配置 \"upgrade-spawnsize\" 来指定团队生成的半径",
        "# \"Smart Item Sharing\" is a system that causes dropped items to be shared between players equally": "# \"智能物品共享\" 是一个使掉落的物品在玩家之间平等共享的系统",
        "# The vanilla minecraft system works by preferring one player while others don't obtain anything": "# 原版 Minecraft 系统通过偏爱一个玩家而其他人什么都得不到来工作",
        "# This only works with items that itemspawners drop": "# 这只适用于物品生成器掉落的物品",

        # STATISTICS 部分
        "# ========== STATISTICS ==========": "# ========== 统计 ==========",
        "# Display their statistics when they're writing /stats while they're inside an arena": "# 当他们在竞技场内输入 /stats 时显示他们的统计信息",
        "# Addition to \"allowcommand-stats\": Allow them to do that even when they're not in an arena": "# \"allowcommand-stats\" 的补充：即使他们不在竞技场中也允许他们这样做",
        "# The statistic which the ranking order will be calculated by": "# 将用于计算排名顺序的统计信息",
        "# How frequently (in minutes) the rank of the player shall be calculated": "# 玩家排名的计算频率（以分钟为单位）",
        "# Setting this to -1 will also completely disable the automatic calculation. You must use /bw tools recalcstats instead to update them": "# 将此设置为 -1 也将完全禁用自动计算。您必须使用 /bw tools recalcstats 来更新它们",
        "# How should the playing time be displayed": "# 应如何显示游戏时间",
        "# Placeholders: {days}, {hours}, {minutes}, {seconds}. Will take them from the configs below": "# 占位符：{days}, {hours}, {minutes}, {seconds}。将从下面的配置中获取它们",
        "# What it should insert for placeholders in \"display-counting-time\"": "# 应为 \"display-counting-time\" 中的占位符插入什么",
        "# If true, we will only insert the \"display-counting-time-entry-*\" placeholders when they're not equal to 0": "# 如果为 true，我们只会在 \"display-counting-time-entry-*\" 占位符不等于 0 时插入它们",
        "# Used to enable a system that prevents player from repeatedly obtaining stats by abusing the system": "# 用于启用防止玩家通过滥用系统重复获得统计信息的系统",
        "# This includes playing a 1v1 with an alt and to make him leave immediately (whereby your main gets a win counted)": "# 这包括与小号进行 1v1 并让他立即离开（从而您的主号获得胜利计数）",
        "# See \"stats-antiabuse-enabled\" to enable this feature and what it is about": "# 请参阅 \"stats-antiabuse-enabled\" 以启用此功能并了解它的内容",
        "# With the ones in the following in particiular, stats don't get counted if the past x rounds were won within y minutes": "# 特别是以下情况，如果过去的 x 轮在 y 分钟内获胜，则统计信息不会被计算",

        # BLOCK BREAK/PLACE 部分
        "# ========== BLOCK BREAK/PLACE ==========": "# ========== 方块破坏/放置 ==========",
        "# What should happen with weird/unusual block drops": "# 奇怪/不寻常的方块掉落应该发生什么",
        "# (like web that drops string or ender chests that drop obsidian)": "# （如掉落线的蜘蛛网或掉落黑曜石的末影箱）",
        "# Possible options:": "# 可能的选项：",
        "#  - KEEP = Don't change the drops": "#  - KEEP = 不改变掉落物",
        "#  - REPLACE_WITH_BLOCK_MATERIAL = Replace the drops with the broken block": "#  - REPLACE_WITH_BLOCK_MATERIAL = 用破坏的方块替换掉落物",
        "#  - REMOVE = Remove the drops completely, so that they drop nothing at all": "#  - REMOVE = 完全移除掉落物，使它们什么都不掉落",
        "# Setting this to false cancels block spreading, this includes e.g.:": "# 将此设置为 false 会取消方块传播，这包括例如：",
        "# - Fire spreading / Fire breaking blocks. It does not prevent fire from existing in the first place": "# - 火焰传播/火焰破坏方块。它不会阻止火焰首先存在",
        "# - Mushrooms spreading": "# - 蘑菇传播",
        "# Setting this to false cancels block forming, this includes e.g.:": "# 将此设置为 false 会取消方块形成，这包括例如：",
        "# - Snow forming due to a snow storm (this may even happen even if no-rain is enabled)": "# - 由于暴雪而形成的雪（即使启用了 no-rain，这也可能发生）",
        "# - Ice forming in a snowy Biome like Taiga or Tundra": "# - 在像针叶林或苔原这样的雪地生物群系中形成的冰",
        "# - Obsidian / Cobblestone forming due to contact with water": "# - 由于与水接触而形成的黑曜石/圆石",
        "# - Concrete forming due to mixing of concrete powder and water": "# - 由于混凝土粉末和水的混合而形成的混凝土",
        "# If this config is enabled, leaves will not decay inside arenas": "# 如果启用此配置，树叶不会在竞技场内腐烂",
        "# If this config is disabled, the configs which start with 'notbuildableradius-' won't work": "# 如果禁用此配置，以 'notbuildableradius-' 开头的配置将不起作用",
        "# The radius in which players aren't to place/break blocks around shops and upgrade shops (0 = disable)": "# 玩家不能在商店和升级商店周围放置/破坏方块的半径（0 = 禁用）",
        "# The radius in which players aren't to place/break blocks at team spawnpoints (0 = disable)": "# 玩家不能在团队生成点放置/破坏方块的半径（0 = 禁用）",
        "# The radius in which players aren't to place/break blocks at itemspawners (0 = disable)": "# 玩家不能在物品生成器处放置/破坏方块的半径（0 = 禁用）",
        "# Prevents water from flowing into the notbuildableradius": "# 防止水流入不可建造半径",
        "# If this config is enabled, and a player is buying for example wool, the wool will be dyed to his team-color": "# 如果启用此配置，并且玩家购买例如羊毛，羊毛将被染成他的团队颜色",
        "# If enabled, blocks will be redyed to a teams color when they are picked up.": "# 如果启用，方块在被拾取时将重新染成团队颜色。",
        "# Note: This has no effect if \"dye-block\" is disabled.": "# 注意：如果禁用 \"dye-block\"，这没有效果。",
        "# By default (this config set to true), only specific block materials are placeable. This includes the ones available in the shop.": "# 默认情况下（此配置设置为 true），只有特定的方块材料是可放置的。这包括商店中可用的材料。",
        "# You may add your own ones to the whitelist using the \"placeableblock-whitelist\" config.": "# 您可以使用 \"placeableblock-whitelist\" 配置将您自己的材料添加到白名单中。",
        "# However, you can completely disable the whitelist by setting this config to false. By this all materials become breakable & placeable.": "# 但是，您可以通过将此配置设置为 false 来完全禁用白名单。通过这种方式，所有材料都变得可破坏和可放置。",
        "# Keep in mind that non-material related configs, such as \"destroyblock-builtbyplayers\", can still have an effect on limiting the blocks that are breakable.": "# 请记住，与材料无关的配置，如 \"destroyblock-builtbyplayers\"，仍然可以对限制可破坏的方块产生影响。",
        "# In this config you're able to add blocks that players can place": "# 在此配置中，您可以添加玩家可以放置的方块",
        "# Purchasable blocks are automatically in this list": "# 可购买的方块自动在此列表中",
        "# Example: placeableblock-whitelist: wood, dark_oak_door, cake": "# 示例：placeableblock-whitelist: wood, dark_oak_door, cake",
        "# If this config is disabled, players can't trample on wheat anymore": "# 如果禁用此配置，玩家不能再践踏小麦",
        "# Players will be able to extinguish fire by left clicking on it": "# 玩家将能够通过左键单击来扑灭火焰",
        "# This config will cause players to only be able to destroy blocks which have been placed by other players in the arena.": "# 此配置将导致玩家只能摧毁竞技场中其他玩家放置的方块。",
        "# By which it's not possible to break the arena.": "# 通过这种方式，不可能破坏竞技场。",
        "# Whether hanging entities (like paintings, item frames, leashes, etc.) are interactable and breakable": "# 悬挂实体（如画、物品展示框、拴绳等）是否可交互和可破坏",
        "# See https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/Hanging.html \"All Known Subinterfaces\" for what counts as hanging": "# 请参阅 https://hub.spigotmc.org/javadocs/spigot/org/bukkit/entity/Hanging.html \"所有已知子接口\" 了解什么算作悬挂",
        "# If this config is enabled, floor foliage (flowers, grass, saplings, ...) won't get in the way of players (during pvp)": "# 如果启用此配置，地面植物（花、草、树苗等）不会妨碍玩家（在 PvP 期间）",
        "# This means that restrictions for destruction of foliage will be lifted and they won't drop anything": "# 这意味着对植物破坏的限制将被解除，它们不会掉落任何东西",

        # EXPLOSIVES 部分
        "# ========== EXPLOSIVES ==========": "# ========== 爆炸物 ==========",
        "# If this config is enabled, TNT will explode after the given time": "# 如果启用此配置，TNT 将在给定时间后爆炸",
        "# The time in seconds after which the TNT will explode": "# TNT 爆炸的时间（以秒为单位）",
        "# If this config is enabled, TNT will break blocks": "# 如果启用此配置，TNT 将破坏方块",
        "# If this config is enabled, TNT will damage players": "# 如果启用此配置，TNT 将伤害玩家",
        "# The damage that TNT will cause to players": "# TNT 对玩家造成的伤害",
        "# If this config is enabled, TNT will cause knockback to players": "# 如果启用此配置，TNT 将对玩家造成击退",
        "# The knockback multiplier for TNT": "# TNT 的击退倍数",
        "# If this config is enabled, TNT will break blocks that were placed by players": "# 如果启用此配置，TNT 将破坏玩家放置的方块",
        "# If this config is enabled, TNT will break blocks that are part of the arena": "# 如果启用此配置，TNT 将破坏属于竞技场的方块",
        "# The radius in which TNT will break blocks": "# TNT 破坏方块的半径",
        "# If this config is enabled, fireballs will explode on impact": "# 如果启用此配置，火球将在撞击时爆炸",
        "# If this config is enabled, fireballs will break blocks": "# 如果启用此配置，火球将破坏方块",
        "# If this config is enabled, fireballs will damage players": "# 如果启用此配置，火球将伤害玩家",
        "# The damage that fireballs will cause to players": "# 火球对玩家造成的伤害",
        "# If this config is enabled, fireballs will cause knockback to players": "# 如果启用此配置，火球将对玩家造成击退",
        "# The knockback multiplier for fireballs": "# 火球的击退倍数",
        "# If this config is enabled, fireballs will break blocks that were placed by players": "# 如果启用此配置，火球将破坏玩家放置的方块",
        "# If this config is enabled, fireballs will break blocks that are part of the arena": "# 如果启用此配置，火球将破坏属于竞技场的方块",
        "# The radius in which fireballs will break blocks": "# 火球破坏方块的半径",

        # DEATH 部分
        "# ========== DEATH ==========": "# ========== 死亡 ==========",
        "# If this config is enabled, players will drop their items when they die": "# 如果启用此配置，玩家死亡时将掉落他们的物品",
        "# If this config is enabled, players will keep their experience when they die": "# 如果启用此配置，玩家死亡时将保留他们的经验",
        "# The time in seconds after which a player will respawn": "# 玩家重生的时间（以秒为单位）",
        "# If this config is enabled, players will be invulnerable for a short time after respawning": "# 如果启用此配置，玩家在重生后将在短时间内无敌",
        "# The time in seconds for which a player will be invulnerable after respawning": "# 玩家重生后无敌的时间（以秒为单位）",
        "# If this config is enabled, players will be teleported to their team spawn when they die": "# 如果启用此配置，玩家死亡时将被传送到他们的团队生成点",
        "# If this config is enabled, players will be able to spectate after they die": "# 如果启用此配置，玩家死亡后将能够观察",
        "# If this config is enabled, spectators will be able to fly": "# 如果启用此配置，观察者将能够飞行",
        "# If this config is enabled, spectators will be invisible to other players": "# 如果启用此配置，观察者对其他玩家将是隐形的",
        "# If this config is enabled, spectators will be able to teleport to other players": "# 如果启用此配置，观察者将能够传送到其他玩家",
        "# The message that will be sent when a player dies": "# 玩家死亡时将发送的消息",
        "# The message that will be sent when a player is eliminated": "# 玩家被淘汰时将发送的消息",
        "# The message that will be sent when a team is eliminated": "# 团队被淘汰时将发送的消息",
        "# The message that will be sent when a player kills another player": "# 玩家杀死另一个玩家时将发送的消息",
        "# The message that will be sent when a player gets a final kill": "# 玩家获得最终击杀时将发送的消息",
        "# The sound that will be played when a player dies": "# 玩家死亡时将播放的声音",
        "# The sound that will be played when a player is eliminated": "# 玩家被淘汰时将播放的声音",
        "# The sound that will be played when a team is eliminated": "# 团队被淘汰时将播放的声音",
        "# The sound that will be played when a player kills another player": "# 玩家杀死另一个玩家时将播放的声音",
        "# The sound that will be played when a player gets a final kill": "# 玩家获得最终击杀时将播放的声音",

        # PERFORMANCE 部分
        "# ========== PERFORMANCE ==========": "# ========== 性能 ==========",
        "# If this config is enabled, the plugin will use async tasks for certain operations": "# 如果启用此配置，插件将对某些操作使用异步任务",
        "# If this config is enabled, the plugin will cache certain data to improve performance": "# 如果启用此配置，插件将缓存某些数据以提高性能",
        "# The maximum number of players that can be in an arena at the same time": "# 同时可以在竞技场中的最大玩家数量",
        "# The maximum number of arenas that can be running at the same time": "# 同时可以运行的最大竞技场数量",
        "# If this config is enabled, the plugin will limit the number of entities in arenas": "# 如果启用此配置，插件将限制竞技场中的实体数量",
        "# The maximum number of entities that can be in an arena": "# 竞技场中可以存在的最大实体数量",
        "# If this config is enabled, the plugin will automatically clean up dropped items": "# 如果启用此配置，插件将自动清理掉落的物品",
        "# The time in seconds after which dropped items will be removed": "# 掉落物品被移除的时间（以秒为单位）",
        "# If this config is enabled, the plugin will use optimized block handling": "# 如果启用此配置，插件将使用优化的方块处理",
        "# If this config is enabled, the plugin will use optimized entity handling": "# 如果启用此配置，插件将使用优化的实体处理",
        "# If this config is enabled, the plugin will use optimized packet handling": "# 如果启用此配置，插件将使用优化的数据包处理",
        "# The interval in ticks for updating certain game mechanics": "# 更新某些游戏机制的间隔（以刻为单位）",
        "# The interval in ticks for updating player statistics": "# 更新玩家统计信息的间隔（以刻为单位）",
        "# The interval in ticks for updating scoreboards": "# 更新记分板的间隔（以刻为单位）",

        # SPECTATING 部分
        "# ========== SPECTATING ==========": "# ========== 观察 ==========",
        "# If this config is enabled, spectators will be able to see other spectators": "# 如果启用此配置，观察者将能够看到其他观察者",
        "# If this config is enabled, spectators will be able to chat with other spectators": "# 如果启用此配置，观察者将能够与其他观察者聊天",
        "# If this config is enabled, spectators will be able to use commands": "# 如果启用此配置，观察者将能够使用命令",
        "# The commands that spectators will be able to use": "# 观察者将能够使用的命令",
        "# If this config is enabled, spectators will be able to interact with the world": "# 如果启用此配置，观察者将能够与世界交互",
        "# If this config is enabled, spectators will be able to pick up items": "# 如果启用此配置，观察者将能够拾取物品",
        "# If this config is enabled, spectators will be able to break blocks": "# 如果启用此配置，观察者将能够破坏方块",
        "# If this config is enabled, spectators will be able to place blocks": "# 如果启用此配置，观察者将能够放置方块",
        "# If this config is enabled, spectators will be able to damage entities": "# 如果启用此配置，观察者将能够伤害实体",
        "# If this config is enabled, spectators will be able to open containers": "# 如果启用此配置，观察者将能够打开容器",
        "# The gamemode that spectators will have": "# 观察者将拥有的游戏模式",
        "# If this config is enabled, spectators will have night vision": "# 如果启用此配置，观察者将拥有夜视",
        "# If this config is enabled, spectators will have speed": "# 如果启用此配置，观察者将拥有速度",
        "# The speed level that spectators will have": "# 观察者将拥有的速度等级",

        # SPECIAL ITEMS 部分
        "# ========== SPECIAL ITEMS ==========": "# ========== 特殊物品 ==========",
        "# If this config is enabled, special items will be available in the game": "# 如果启用此配置，游戏中将提供特殊物品",
        "# The items that will be given to players when they join a team": "# 玩家加入团队时将给予的物品",
        "# The items that will be given to players when they leave a team": "# 玩家离开团队时将给予的物品",
        "# The items that will be given to spectators": "# 将给予观察者的物品",
        "# If this config is enabled, players will be able to use the team selector": "# 如果启用此配置，玩家将能够使用团队选择器",
        "# If this config is enabled, players will be able to use the leave item": "# 如果启用此配置，玩家将能够使用离开物品",
        "# If this config is enabled, players will be able to use the stats item": "# 如果启用此配置，玩家将能够使用统计物品",
        "# If this config is enabled, players will be able to use the shop item": "# 如果启用此配置，玩家将能够使用商店物品",
        "# If this config is enabled, players will be able to use the upgrade item": "# 如果启用此配置，玩家将能够使用升级物品",
        "# If this config is enabled, spectators will be able to use the teleporter item": "# 如果启用此配置，观察者将能够使用传送器物品",
        "# If this config is enabled, spectators will be able to use the play again item": "# 如果启用此配置，观察者将能够使用再次游戏物品",
        "# If this config is enabled, spectators will be able to use the leave item": "# 如果启用此配置，观察者将能够使用离开物品",
        "# The material of the team selector item": "# 团队选择器物品的材料",
        "# The material of the leave item": "# 离开物品的材料",
        "# The material of the stats item": "# 统计物品的材料",
        "# The material of the shop item": "# 商店物品的材料",
        "# The material of the upgrade item": "# 升级物品的材料",
        "# The material of the teleporter item": "# 传送器物品的材料",
        "# The material of the play again item": "# 再次游戏物品的材料",
    }
    
    # 应用翻译
    for english, chinese in comment_translations.items():
        content = content.replace(english, chinese)
    
    # 保存翻译后的文件
    with open('config.yml', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Config.yml 翻译完成！")
    print("原文件已备份为 config.yml.backup")

if __name__ == "__main__":
    translate_config()
